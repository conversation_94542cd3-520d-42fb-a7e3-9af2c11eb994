#!/usr/bin/env python3
"""
最终验证脚本 - 确保所有功能正常工作
"""

import os
import sys
import subprocess

def test_imports():
    """测试所有必需的模块是否可以正常导入"""
    print("🔍 测试模块导入...")
    
    try:
        import pandas as pd
        print("✅ pandas 导入成功")
        
        import openpyxl
        print("✅ openpyxl 导入成功")
        
        import tkinter as tk
        from tkinter import filedialog, messagebox
        print("✅ tkinter 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_file_structure():
    """检查必要文件是否存在"""
    print("\n📁 检查文件结构...")
    
    required_files = [
        "process_excel.py",
        "requirements.txt", 
        "build.bat",
        "build_exe.py",
        "build_windows.py"
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 缺失")
            all_exist = False
    
    return all_exist

def test_gui_parameters():
    """测试GUI参数是否正确"""
    print("\n🖥️ 测试GUI参数...")
    
    try:
        import tkinter as tk
        from tkinter import filedialog
        
        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 测试参数（不实际显示对话框）
        # 这里只是验证参数名称是否正确
        params = {
            'title': "测试",
            'defaultextension': ".xlsx",
            'initialfile': "test.xlsx",  # 关键修复：使用 initialfile 而不是 initialfilename
            'filetypes': [("Excel文件", "*.xlsx")]
        }
        
        # 验证参数名称
        valid_params = ['defaultextension', 'filetypes', 'initialdir', 'initialfile', 'message', 'parent', 'title', 'typevariable', 'command']
        
        for param in params.keys():
            if param in valid_params:
                print(f"✅ 参数 '{param}' 正确")
            else:
                print(f"❌ 参数 '{param}' 无效")
                return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI参数测试失败: {e}")
        return False

def test_pyinstaller():
    """检查PyInstaller是否可用"""
    print("\n📦 检查PyInstaller...")
    
    try:
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ PyInstaller 可用，版本: {version}")
            return True
        else:
            print("❌ PyInstaller 不可用")
            return False
    except Exception as e:
        print(f"❌ PyInstaller 检查失败: {e}")
        return False

def main():
    print("=" * 60)
    print("Excel处理工具 - 最终验证测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("文件结构", test_file_structure), 
        ("GUI参数", test_gui_parameters),
        ("PyInstaller", test_pyinstaller)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以进行打包部署")
        print("\n📋 下一步操作:")
        print("1. 在Windows 11上运行 build.bat")
        print("2. 或者运行: python build_windows.py")
        print("3. 获取 dist/Excel处理工具.exe")
    else:
        print("⚠️ 部分测试失败，请检查并修复问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

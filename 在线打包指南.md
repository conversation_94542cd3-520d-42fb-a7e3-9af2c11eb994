# Excel处理工具 - 在线Windows环境打包指南

## 🎯 目标
在没有Windows电脑的情况下，使用在线Windows环境打包exe文件

## 🌐 推荐的在线Windows环境

### 1. GitHub Codespaces（推荐）
- **优点**: 免费额度，集成Git，环境稳定
- **网址**: https://github.com/codespaces
- **步骤**:
  1. 创建GitHub仓库并上传代码
  2. 创建Codespace
  3. 选择Windows环境
  4. 运行打包命令

### 2. Replit
- **优点**: 简单易用，支持多种语言
- **网址**: https://replit.com
- **限制**: 可能需要付费版本支持Windows环境

### 3. CodeSandbox
- **优点**: 在线IDE，支持多种环境
- **网址**: https://codesandbox.io
- **适用**: 适合简单的Python项目

## 📋 详细操作步骤（以GitHub Codespaces为例）

### 步骤1: 准备GitHub仓库

1. **创建GitHub账号**（如果没有）
   - 访问 https://github.com
   - 注册免费账号

2. **创建新仓库**
   - 点击 "New repository"
   - 仓库名: `excel-processor`
   - 设为Public（免费用户）
   - 勾选 "Add a README file"

3. **上传代码文件**
   - 点击 "uploading an existing file"
   - 上传以下文件：
     ```
     process_excel.py
     requirements.txt
     build.bat
     build_windows.py
     README.md
     部署说明.md
     ```

### 步骤2: 创建Codespace

1. **进入仓库页面**
   - 点击绿色的 "Code" 按钮
   - 选择 "Codespaces" 标签
   - 点击 "Create codespace on main"

2. **等待环境启动**
   - 系统会自动创建在线开发环境
   - 通常需要1-2分钟

### 步骤3: 配置Windows环境

1. **检查系统**
   ```bash
   uname -a
   python --version
   ```

2. **如果是Linux环境，需要安装Wine**
   ```bash
   sudo apt update
   sudo apt install wine
   ```

### 步骤4: 安装依赖并打包

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行打包脚本**
   ```bash
   python build_windows.py
   ```

3. **或者手动打包**
   ```bash
   python -m PyInstaller --onefile --windowed --name="Excel处理工具" process_excel.py
   ```

### 步骤5: 下载exe文件

1. **查看生成的文件**
   ```bash
   ls -la dist/
   ```

2. **下载文件**
   - 在文件浏览器中找到 `dist/Excel处理工具.exe`
   - 右键点击文件
   - 选择 "Download"

## 🔄 方案二：使用Docker（高级用户）

### 创建Windows容器

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/windows/servercore:ltsc2019

# 安装Python
RUN powershell -Command \
    Invoke-WebRequest -Uri https://www.python.org/ftp/python/3.9.0/python-3.9.0-amd64.exe -OutFile python-installer.exe; \
    Start-Process python-installer.exe -ArgumentList '/quiet InstallAllUsers=1 PrependPath=1' -Wait; \
    Remove-Item python-installer.exe

# 复制代码
COPY . /app
WORKDIR /app

# 安装依赖并打包
RUN pip install -r requirements.txt
RUN python -m PyInstaller --onefile --windowed --name="Excel处理工具" process_excel.py
```

### 运行命令
```bash
docker build -t excel-processor .
docker run -v $(pwd)/dist:/app/dist excel-processor
```

## 🚀 方案三：使用GitHub Actions自动打包

让我为您创建一个GitHub Actions工作流：

```yaml
# .github/workflows/build.yml
name: Build Windows EXE

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Build EXE
      run: |
        python -m PyInstaller --onefile --windowed --name="Excel处理工具" process_excel.py
    
    - name: Upload artifact
      uses: actions/upload-artifact@v3
      with:
        name: Excel处理工具
        path: dist/Excel处理工具.exe
```

## 💡 推荐方案总结

**最简单**: GitHub Codespaces
- 免费
- 无需本地安装
- 直接在浏览器中操作

**最自动化**: GitHub Actions
- 完全自动化
- 每次代码更新自动构建
- 可下载构建产物

**最灵活**: 在线IDE + Wine
- 可以在Linux环境中模拟Windows
- 适合有技术背景的用户

## 📞 需要帮助？

如果您在操作过程中遇到问题，请提供：
1. 使用的平台（GitHub Codespaces/Replit等）
2. 错误信息截图
3. 操作步骤描述

我可以为您提供更详细的指导！

#!/usr/bin/env python3
"""
快速测试脚本 - 验证修复后的代码
"""

import tkinter as tk
from tkinter import filedialog, messagebox

def test_save_dialog():
    """测试保存对话框的参数是否正确"""
    root = tk.Tk()
    root.withdraw()
    
    try:
        # 测试修复后的参数
        save_path = filedialog.asksaveasfilename(
            title="测试保存对话框",
            defaultextension=".xlsx",
            initialfile="测试文件-人力.xlsx",  # 使用 initialfile 而不是 initialfilename
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        
        if save_path:
            messagebox.showinfo("测试成功", f"保存对话框工作正常！\n选择的路径: {save_path}")
        else:
            messagebox.showinfo("测试完成", "用户取消了保存操作")
            
        print("✅ 保存对话框测试通过 - 参数修复成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        messagebox.showerror("测试失败", f"错误: {e}")
    
    finally:
        root.destroy()

if __name__ == "__main__":
    print("正在测试修复后的保存对话框...")
    test_save_dialog()
    print("测试完成")

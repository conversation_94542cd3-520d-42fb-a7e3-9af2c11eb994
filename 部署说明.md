# Excel处理工具 - Windows部署说明

## 📋 概述

这是一个专门用于处理Excel文件的工具，可以自动删除指定的列（"预期评分"、"折扣"、"调整奖金"）并将公式转换为数值。本文档详细说明如何在Windows 11系统上打包和部署此工具。

## 🎯 功能特点

- ✅ **智能列检测**: 自动在Excel文件的前几行中查找目标列标题
- ✅ **多工作表支持**: 处理Excel文件中的所有工作表
- ✅ **公式转数值**: 将所有公式转换为计算后的数值
- ✅ **精确删除**: 只删除包含"预期评分"、"折扣"、"调整奖金"的列
- ✅ **图形界面**: 提供友好的文件选择和保存界面
- ✅ **安全处理**: 不修改原文件，生成新的处理后文件

## 🛠️ 环境要求

### 开发环境（用于打包）
- **操作系统**: Windows 11 (或Windows 10)
- **Python版本**: Python 3.7 或更高版本
- **必需包**: pandas, openpyxl, pyinstaller

### 运行环境（最终用户）
- **操作系统**: Windows 11 (或Windows 10)
- **无需安装Python**: exe文件包含所有依赖

## 📦 打包步骤

### 方法一：使用批处理文件（推荐）

1. **准备文件**
   ```
   确保以下文件在同一目录中：
   - process_excel.py
   - requirements.txt
   - build.bat
   ```

2. **运行打包脚本**
   - 双击 `build.bat` 文件
   - 按照屏幕提示操作
   - 等待打包完成

3. **获取结果**
   - 打包完成后，在 `dist` 目录中找到 `Excel处理工具.exe`

### 方法二：使用Python脚本

1. **运行打包脚本**
   ```cmd
   python build_windows.py
   ```

2. **或者使用通用脚本**
   ```cmd
   python build_exe.py
   ```

### 方法三：手动打包

1. **安装依赖**
   ```cmd
   pip install -r requirements.txt
   ```

2. **执行打包命令**
   ```cmd
   python -m PyInstaller --onefile --windowed --name="Excel处理工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=tkinter --hidden-import=tkinter.filedialog --hidden-import=tkinter.messagebox --collect-all=pandas --collect-all=openpyxl --noconfirm process_excel.py
   ```

3. **清理临时文件**
   ```cmd
   rmdir /s /q build
   del "Excel处理工具.spec"
   ```

## 🚀 部署步骤

### 1. 复制文件
将生成的 `Excel处理工具.exe` 复制到目标Windows电脑

### 2. 运行程序
双击 `Excel处理工具.exe` 启动程序

### 3. 使用流程
1. 程序启动后会弹出文件选择对话框
2. 选择要处理的Excel文件（支持.xlsx和.xls格式）
3. 选择保存位置和文件名
4. 等待处理完成
5. 查看处理结果

## 🧪 测试

### 打包前测试
```cmd
python test_gui.py
```
此脚本用于测试图形界面组件是否正常工作

### 功能测试
```cmd
python process_excel.py
```
直接运行主程序进行功能测试

## 📁 文件结构

```
excel-processor/
├── process_excel.py          # 主程序文件
├── requirements.txt          # Python依赖包列表
├── build.bat                # Windows批处理打包脚本
├── build_exe.py             # 通用Python打包脚本
├── build_windows.py         # Windows专用Python打包脚本
├── test_gui.py              # GUI测试脚本
├── README.md                # 英文说明文档
├── 部署说明.md              # 中文部署说明
├── build/                   # 临时构建目录（自动生成）
└── dist/                    # 输出目录
    └── Excel处理工具.exe    # 最终生成的可执行文件
```

## ⚠️ 注意事项

### 打包注意事项
- 确保Python环境正确安装并添加到PATH
- 网络连接正常（用于下载依赖包）
- 杀毒软件可能会误报，需要添加信任
- 打包过程可能需要几分钟时间

### 使用注意事项
- 程序会保留原文件不变，生成新的处理后文件
- 如果工作表中没有找到目标列，会自动跳过该工作表
- 处理大文件时可能需要较长时间，请耐心等待
- 建议在处理重要文件前先备份

### 兼容性
- 支持Excel 2007及以上版本的.xlsx文件
- 支持Excel 97-2003的.xls文件
- 兼容Windows 10和Windows 11

## 🔧 故障排除

### 常见问题及解决方案

#### 1. "未找到Python环境"
**解决方案:**
- 安装Python 3.7或更高版本
- 安装时勾选"Add Python to PATH"选项
- 重启命令提示符

#### 2. "安装依赖包失败"
**解决方案:**
- 检查网络连接
- 使用国内镜像：
  ```cmd
  pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
  ```
- 以管理员身份运行

#### 3. "打包失败"
**解决方案:**
- 确保所有依赖包已正确安装
- 检查是否有杀毒软件阻止
- 尝试以管理员身份运行
- 检查磁盘空间是否充足

#### 4. "exe文件无法运行"
**解决方案:**
- 检查Windows版本兼容性
- 尝试以管理员身份运行
- 检查是否被杀毒软件误报并添加信任
- 确保目标电脑支持64位程序

#### 5. "处理Excel文件失败"
**解决方案:**
- 确保Excel文件没有被其他程序占用
- 检查Excel文件是否损坏
- 确保有足够的磁盘空间保存结果文件
- 检查文件路径中是否包含特殊字符

## 📞 技术支持

如遇到其他问题，请提供以下信息：
- Windows版本
- Python版本（如果适用）
- 错误信息截图
- 操作步骤描述

## 🔄 版本更新记录

### v1.2.1 (最新)
- ✅ 修复了文件保存对话框参数错误的问题
- ✅ 将 `initialfilename` 参数修正为 `initialfile`
- ✅ 改进了错误处理和用户体验

### v1.2.0
- ✅ 添加图形界面，支持文件选择和保存对话框
- ✅ 改进列检测逻辑，支持更复杂的Excel文件结构

### v1.1.0
- ✅ 支持基本的Excel列删除和公式转换功能

## 📄 许可证

MIT License - 可自由使用、修改和分发

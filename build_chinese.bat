@echo off
:: 专门为中文Windows系统设计的打包脚本
:: 使用GBK编码避免乱码问题
chcp 936 >nul 2>&1
title Excel处理工具打包脚本

cls
echo ============================================================
echo                Excel处理工具自动打包脚本
echo ============================================================
echo.
echo 本脚本将自动完成以下操作:
echo 1. 检查Python环境
echo 2. 安装必要的依赖包
echo 3. 使用PyInstaller打包成exe文件
echo 4. 清理临时文件
echo.
echo 请确保您已经安装了Python 3.7或更高版本
echo.
pause

echo.
echo [1/4] 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python环境
    echo.
    echo 请先安装Python 3.7或更高版本:
    echo https://www.python.org/downloads/
    echo.
    echo 安装时请勾选 "Add Python to PATH" 选项
    echo.
    pause
    exit /b 1
)

python --version
echo [成功] Python环境检查通过

echo.
echo [2/4] 正在安装依赖包...
echo 正在升级pip...
python -m pip install --upgrade pip >nul 2>&1

echo 正在安装项目依赖...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo [错误] 安装依赖包失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接
    echo 2. 使用国内镜像源重试
    echo 3. 以管理员身份运行此脚本
    echo.
    echo 是否尝试使用国内镜像源? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在使用清华大学镜像源重试...
        python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if errorlevel 1 (
            echo [错误] 使用镜像源仍然失败
            pause
            exit /b 1
        )
    ) else (
        pause
        exit /b 1
    )
)
echo [成功] 依赖包安装完成

echo.
echo [3/4] 正在打包exe文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

python -m PyInstaller --onefile --windowed --name="Excel处理工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=tkinter --hidden-import=tkinter.filedialog --hidden-import=tkinter.messagebox --collect-all=pandas --collect-all=openpyxl --noconfirm process_excel.py

if errorlevel 1 (
    echo [错误] 打包失败
    echo.
    echo 可能的解决方案:
    echo 1. 确保所有依赖包已正确安装
    echo 2. 检查是否有杀毒软件阻止
    echo 3. 尝试以管理员身份运行
    echo 4. 检查磁盘空间是否充足
    echo.
    pause
    exit /b 1
)
echo [成功] 打包完成

echo.
echo [4/4] 正在清理临时文件...
if exist build (
    rmdir /s /q build >nul 2>&1
    echo [成功] 已删除build目录
)
if exist "Excel处理工具.spec" (
    del "Excel处理工具.spec" >nul 2>&1
    echo [成功] 已删除spec文件
)

echo.
echo ============================================================
echo                        打包完成!
echo ============================================================
echo.

if exist "dist\Excel处理工具.exe" (
    echo [成功] 可执行文件已生成: dist\Excel处理工具.exe
    echo.
    echo 文件信息:
    for %%i in ("dist\Excel处理工具.exe") do (
        set /a size_mb=%%~zi/1048576
        echo    文件大小: %%~zi 字节 (约 !size_mb! MB)
    )
    echo    文件位置: %CD%\dist\Excel处理工具.exe
    echo.
    echo 使用说明:
    echo 1. 将 'Excel处理工具.exe' 复制到目标电脑
    echo 2. 双击运行exe文件
    echo 3. 按照提示选择Excel文件和保存位置
    echo 4. 等待处理完成
    echo.
    echo 功能说明:
    echo - 支持 .xlsx 和 .xls 格式的Excel文件
    echo - 程序会自动删除"预期评分"、"折扣"、"调整奖金"列
    echo - 所有公式会转换为数值
    echo - 原文件不会被修改，会生成新的处理后文件
    echo.
    echo [提示] 如果Windows Defender或其他杀毒软件报警，
    echo        请选择"允许"或将文件添加到信任列表
) else (
    echo [警告] 未找到生成的exe文件
    echo.
    echo 请检查以下内容:
    echo 1. 是否有错误信息
    echo 2. dist目录是否存在
    echo 3. 杀毒软件是否阻止了文件生成
    echo.
    if exist dist (
        echo dist目录内容:
        dir dist
    ) else (
        echo dist目录不存在，打包可能失败
    )
)

echo.
echo ============================================================
echo 按任意键退出...
pause >nul

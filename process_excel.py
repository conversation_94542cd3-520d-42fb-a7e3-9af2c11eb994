import pandas as pd
import os
from openpyxl import load_workbook

def process_excel(file_path):
    # 获取文件名和路径
    file_dir, file_name = os.path.split(file_path)
    file_base, file_ext = os.path.splitext(file_name)
    new_file_name = f"{file_base}-人力{file_ext}"
    new_file_path = os.path.join(file_dir, new_file_name)
    
    # 加载工作簿，保留值而不是公式
    wb = load_workbook(file_path, data_only=True)
    
    # 处理每个sheet
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        
        # 查找要删除的列
        cols_to_delete = []
        for col in range(1, ws.max_column + 1):
            header = ws.cell(row=1, col=col).value
            if header in ["预期评分", "折扣", "调整奖金"]:
                cols_to_delete.append(col)
        
        # 从后往前删除列(避免索引变化)
        for col in sorted(cols_to_delete, reverse=True):
            ws.delete_cols(col)
    
    # 保存新文件
    wb.save(new_file_path)
    return new_file_path

# 使用示例
if __name__ == "__main__":
    file_path = input("请输入Excel文件路径: ")
    new_file = process_excel(file_path)
    print(f"处理完成，新文件保存为: {new_file}")
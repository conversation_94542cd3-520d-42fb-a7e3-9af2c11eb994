import pandas as pd
import os
from openpyxl import load_workbook

def process_excel(file_path):
    # 获取文件名和路径
    file_dir, file_name = os.path.split(file_path)
    file_base, file_ext = os.path.splitext(file_name)
    new_file_name = f"{file_base}-人力{file_ext}"
    new_file_path = os.path.join(file_dir, new_file_name)
    
    # 加载工作簿，保留值而不是公式
    wb = load_workbook(file_path, data_only=True)
    
    # 处理每个sheet
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        print(f"正在处理工作表: {sheet_name}")

        # 查找要删除的列
        cols_to_delete = []

        # 检查前几行来找到真正的列标题
        header_row = None
        for row in range(1, min(6, ws.max_row + 1)):  # 检查前5行
            row_has_target_headers = False
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=row, column=col).value
                if cell_value and str(cell_value).strip() in ["预期评分", "折扣", "调整奖金"]:
                    row_has_target_headers = True
                    break

            if row_has_target_headers:
                header_row = row
                break

        if header_row is None:
            print(f"  - 未找到目标列，跳过")
            continue

        # 在找到的标题行中查找要删除的列
        for col in range(1, ws.max_column + 1):
            header = ws.cell(row=header_row, column=col).value
            if header and str(header).strip() in ["预期评分", "折扣", "调整奖金"]:
                cols_to_delete.append(col)

        # 从后往前删除列(避免索引变化)
        if cols_to_delete:
            print(f"  - 删除了 {len(cols_to_delete)} 列: {[ws.cell(row=header_row, column=col).value for col in cols_to_delete]}")
            for col in sorted(cols_to_delete, reverse=True):
                ws.delete_cols(col)
        else:
            print(f"  - 未找到需要删除的列")
    
    # 保存新文件
    wb.save(new_file_path)
    return new_file_path

# 使用示例
if __name__ == "__main__":
    file_path = "/Users/<USER>/Library/CloudStorage/SynologyDrive-HX/个人/2025/美铃绩效文档/省级拓展中心绩效考评表-5月.xlsx"
    new_file = process_excel(file_path)
    print(f"处理完成，新文件保存为: {new_file}")
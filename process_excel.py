import pandas as pd
import os
from openpyxl import load_workbook
import tkinter as tk
from tkinter import filedialog, messagebox
import sys

def process_excel(file_path, save_path=None):
    # 获取文件名和路径
    file_dir, file_name = os.path.split(file_path)
    file_base, file_ext = os.path.splitext(file_name)

    # 如果没有指定保存路径，则使用默认路径
    if save_path is None:
        new_file_name = f"{file_base}-人力{file_ext}"
        new_file_path = os.path.join(file_dir, new_file_name)
    else:
        new_file_path = save_path
    
    # 加载工作簿，保留值而不是公式
    wb = load_workbook(file_path, data_only=True)
    
    # 处理每个sheet
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        print(f"正在处理工作表: {sheet_name}")

        # 查找要删除的列
        cols_to_delete = []

        # 检查前几行来找到真正的列标题
        header_row = None
        for row in range(1, min(6, ws.max_row + 1)):  # 检查前5行
            row_has_target_headers = False
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=row, column=col).value
                if cell_value and str(cell_value).strip() in ["预期评分", "折扣", "调整奖金"]:
                    row_has_target_headers = True
                    break

            if row_has_target_headers:
                header_row = row
                break

        if header_row is None:
            print(f"  - 未找到目标列，跳过")
            continue

        # 在找到的标题行中查找要删除的列
        for col in range(1, ws.max_column + 1):
            header = ws.cell(row=header_row, column=col).value
            if header and str(header).strip() in ["预期评分", "折扣", "调整奖金"]:
                cols_to_delete.append(col)

        # 从后往前删除列(避免索引变化)
        if cols_to_delete:
            print(f"  - 删除了 {len(cols_to_delete)} 列: {[ws.cell(row=header_row, column=col).value for col in cols_to_delete]}")
            for col in sorted(cols_to_delete, reverse=True):
                ws.delete_cols(col)
        else:
            print(f"  - 未找到需要删除的列")
    
    # 保存新文件
    wb.save(new_file_path)
    return new_file_path

def main():
    # 创建主窗口（隐藏）
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    try:
        # 选择要处理的Excel文件
        messagebox.showinfo("Excel处理工具", "请选择要处理的Excel文件")
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if not file_path:
            messagebox.showinfo("提示", "未选择文件，程序退出")
            return

        # 获取原文件信息
        file_dir, file_name = os.path.split(file_path)
        file_base, file_ext = os.path.splitext(file_name)
        default_name = f"{file_base}-人力{file_ext}"

        # 选择保存路径
        save_path = filedialog.asksaveasfilename(
            title="选择保存位置",
            defaultextension=".xlsx",
            initialfilename=default_name,
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )

        if not save_path:
            messagebox.showinfo("提示", "未选择保存位置，程序退出")
            return

        # 显示处理进度
        progress_window = tk.Toplevel(root)
        progress_window.title("处理中...")
        progress_window.geometry("300x100")
        progress_window.resizable(False, False)

        # 居中显示
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (300 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (100 // 2)
        progress_window.geometry(f"300x100+{x}+{y}")

        progress_label = tk.Label(progress_window, text="正在处理Excel文件，请稍候...", font=("Arial", 12))
        progress_label.pack(expand=True)

        progress_window.update()

        # 处理Excel文件
        result_path = process_excel(file_path, save_path)

        # 关闭进度窗口
        progress_window.destroy()

        # 显示完成消息
        messagebox.showinfo("处理完成", f"文件处理完成！\n\n保存位置：\n{result_path}")

    except Exception as e:
        messagebox.showerror("错误", f"处理文件时发生错误：\n{str(e)}")

    finally:
        root.destroy()

# 使用示例
if __name__ == "__main__":
    main()
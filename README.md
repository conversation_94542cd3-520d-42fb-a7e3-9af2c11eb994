# Excel处理工具

这是一个用于处理Excel文件的工具，可以自动删除指定的列（"预期评分"、"折扣"、"调整奖金"）并将公式转换为数值。

## 功能特点

- 🔍 **智能列检测**: 自动在Excel文件的前几行中查找目标列标题
- 📊 **多工作表支持**: 处理Excel文件中的所有工作表
- 🧮 **公式转数值**: 将所有公式转换为计算后的数值
- 🗑️ **精确删除**: 只删除包含"预期评分"、"折扣"、"调整奖金"的列
- 💻 **图形界面**: 提供友好的文件选择和保存界面
- 📁 **安全处理**: 不修改原文件，生成新的处理后文件

## 使用方法

### 方法一：直接运行exe文件（推荐）

1. 双击运行 `Excel处理工具.exe`
2. 在弹出的对话框中选择要处理的Excel文件
3. 选择保存位置和文件名
4. 等待处理完成
5. 查看处理结果

### 方法二：运行Python脚本

```bash
python process_excel.py
```

## 打包说明

### 环境要求

- Python 3.7+
- Windows 11 (或其他Windows版本)

### 自动打包（推荐）

1. **使用批处理文件**:
   ```cmd
   build.bat
   ```

2. **使用Python脚本**:
   ```bash
   python build_exe.py
   ```

### 手动打包

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **使用PyInstaller打包**:
   ```bash
   pyinstaller --onefile --windowed --name="Excel处理工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=tkinter process_excel.py
   ```

3. **查找生成的exe文件**:
   生成的exe文件位于 `dist/Excel处理工具.exe`

## 文件结构

```
excel-processor/
├── process_excel.py      # 主程序文件
├── requirements.txt      # Python依赖包列表
├── build_exe.py         # Python打包脚本
├── build.bat            # Windows批处理打包脚本
├── README.md            # 说明文档
└── dist/                # 打包输出目录
    └── Excel处理工具.exe # 生成的可执行文件
```

## 依赖包

- `pandas>=2.0.0` - Excel文件处理
- `openpyxl>=3.0.0` - Excel文件读写
- `pyinstaller>=5.0.0` - 打包工具

## 处理逻辑

1. **文件加载**: 使用openpyxl加载Excel文件，设置`data_only=True`将公式转为数值
2. **工作表遍历**: 遍历所有工作表
3. **列标题检测**: 在每个工作表的前5行中查找目标列标题
4. **列删除**: 找到目标列后从后往前删除（避免索引变化）
5. **文件保存**: 保存处理后的文件到指定位置

## 支持的文件格式

- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

## 注意事项

- 程序会保留原文件不变，生成新的处理后文件
- 如果工作表中没有找到目标列，会自动跳过该工作表
- 处理大文件时可能需要较长时间，请耐心等待
- 建议在处理重要文件前先备份

## 故障排除

### 常见问题

1. **"未找到Python环境"**
   - 确保已安装Python 3.7+
   - 确保Python已添加到系统PATH

2. **"安装依赖包失败"**
   - 检查网络连接
   - 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

3. **"打包失败"**
   - 确保所有依赖包已正确安装
   - 检查是否有杀毒软件阻止

4. **exe文件无法运行**
   - 检查Windows版本兼容性
   - 尝试以管理员身份运行
   - 检查是否被杀毒软件误报

## 版本历史

- v1.0.0: 初始版本，支持基本的Excel列删除和公式转换功能
- v1.1.0: 添加图形界面，支持文件选择和保存对话框
- v1.2.0: 改进列检测逻辑，支持更复杂的Excel文件结构

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发者。

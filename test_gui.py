#!/usr/bin/env python3
"""
测试GUI界面的脚本
用于在打包前测试图形界面是否正常工作
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import os

def test_file_dialog():
    """测试文件选择对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    try:
        # 测试文件选择
        messagebox.showinfo("测试", "即将测试文件选择对话框")
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if file_path:
            messagebox.showinfo("选择结果", f"您选择的文件:\n{file_path}")
            
            # 测试保存对话框
            file_dir, file_name = os.path.split(file_path)
            file_base, file_ext = os.path.splitext(file_name)
            default_name = f"{file_base}-人力{file_ext}"
            
            save_path = filedialog.asksaveasfilename(
                title="选择保存位置",
                defaultextension=".xlsx",
                initialfile=default_name,
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )
            
            if save_path:
                messagebox.showinfo("保存位置", f"保存位置:\n{save_path}")
            else:
                messagebox.showinfo("提示", "未选择保存位置")
        else:
            messagebox.showinfo("提示", "未选择文件")
            
    except Exception as e:
        messagebox.showerror("错误", f"测试过程中发生错误:\n{str(e)}")
    
    finally:
        root.destroy()

def test_progress_window():
    """测试进度窗口"""
    root = tk.Tk()
    root.withdraw()
    
    try:
        # 显示进度窗口
        progress_window = tk.Toplevel(root)
        progress_window.title("处理中...")
        progress_window.geometry("300x100")
        progress_window.resizable(False, False)
        
        # 居中显示
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (300 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (100 // 2)
        progress_window.geometry(f"300x100+{x}+{y}")
        
        progress_label = tk.Label(progress_window, text="正在处理Excel文件，请稍候...", font=("Arial", 12))
        progress_label.pack(expand=True)
        
        # 显示3秒后关闭
        progress_window.after(3000, progress_window.destroy)
        progress_window.mainloop()
        
        messagebox.showinfo("测试完成", "进度窗口测试完成")
        
    except Exception as e:
        messagebox.showerror("错误", f"测试进度窗口时发生错误:\n{str(e)}")
    
    finally:
        root.destroy()

def main():
    print("=" * 50)
    print("Excel处理工具 - GUI测试脚本")
    print("=" * 50)
    print()
    print("此脚本用于测试图形界面组件是否正常工作")
    print("请选择要测试的功能:")
    print("1. 测试文件选择对话框")
    print("2. 测试进度窗口")
    print("3. 测试所有功能")
    print("0. 退出")
    print()
    
    while True:
        choice = input("请输入选择 (0-3): ").strip()
        
        if choice == "0":
            print("退出测试")
            break
        elif choice == "1":
            print("测试文件选择对话框...")
            test_file_dialog()
        elif choice == "2":
            print("测试进度窗口...")
            test_progress_window()
        elif choice == "3":
            print("测试所有功能...")
            test_file_dialog()
            test_progress_window()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()

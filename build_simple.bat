@echo off
:: 简化版打包脚本 - 避免编码问题
setlocal enabledelayedexpansion

:: 尝试设置UTF-8编码，如果失败则使用系统默认编码
chcp 65001 >nul 2>&1
if errorlevel 1 chcp 936 >nul 2>&1

cls
echo ============================================================
echo           Excel Processor - Build Script
echo ============================================================
echo.

:: 检查Python
echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python 3.7+ from https://www.python.org/
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)
echo Python OK

:: 安装依赖
echo.
echo Installing dependencies...
python -m pip install --upgrade pip >nul
python -m pip install pandas openpyxl pyinstaller
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo Trying with China mirror...
    python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas openpyxl pyinstaller
    if errorlevel 1 (
        echo ERROR: Still failed. Please check your network connection.
        pause
        exit /b 1
    )
)
echo Dependencies OK

:: 打包
echo.
echo Building EXE file...
echo This may take a few minutes...
python -m PyInstaller --onefile --windowed --name=ExcelProcessor process_excel.py --noconfirm
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

:: 清理
echo.
echo Cleaning up...
if exist build rmdir /s /q build >nul 2>&1
if exist ExcelProcessor.spec del ExcelProcessor.spec >nul 2>&1

:: 检查结果
echo.
echo ============================================================
if exist "dist\ExcelProcessor.exe" (
    echo SUCCESS: ExcelProcessor.exe created in dist folder
    echo.
    echo File location: %CD%\dist\ExcelProcessor.exe
    for %%i in ("dist\ExcelProcessor.exe") do echo File size: %%~zi bytes
    echo.
    echo You can now use ExcelProcessor.exe on any Windows computer
    echo No Python installation required on target computer
) else (
    echo ERROR: ExcelProcessor.exe not found
    echo Please check for error messages above
)

echo ============================================================
echo Press any key to exit...
pause >nul

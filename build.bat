@echo off
chcp 65001 >nul
title Excel处理工具 - 自动打包脚本

echo ============================================================
echo                Excel处理工具 - 自动打包脚本
echo ============================================================
echo.
echo 此脚本将自动完成以下操作:
echo 1. 检查Python环境
echo 2. 安装必要的依赖包
echo 3. 使用PyInstaller打包成exe文件
echo 4. 清理临时文件
echo.
echo 请确保您已经安装了Python 3.7或更高版本
echo.
pause

echo.
echo [1/4] 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo.
    echo 请先安装Python 3.7或更高版本:
    echo https://www.python.org/downloads/
    echo.
    echo 安装时请勾选 "Add Python to PATH" 选项
    pause
    exit /b 1
)

python --version
echo ✅ Python环境检查通过

echo.
echo [2/4] 正在安装依赖包...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 错误: 安装依赖包失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接
    echo 2. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    echo 3. 以管理员身份运行此脚本
    pause
    exit /b 1
)
echo ✅ 依赖包安装完成

echo.
echo [3/4] 正在打包exe文件...
echo 这可能需要几分钟时间，请耐心等待...
python -m PyInstaller --onefile --windowed --name="Excel处理工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=tkinter --hidden-import=tkinter.filedialog --hidden-import=tkinter.messagebox --collect-all=pandas --collect-all=openpyxl --noconfirm process_excel.py

if errorlevel 1 (
    echo ❌ 错误: 打包失败
    echo.
    echo 可能的解决方案:
    echo 1. 确保所有依赖包已正确安装
    echo 2. 检查是否有杀毒软件阻止
    echo 3. 尝试以管理员身份运行
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [4/4] 正在清理临时文件...
if exist build (
    rmdir /s /q build
    echo ✅ 已删除build目录
)
if exist "Excel处理工具.spec" (
    del "Excel处理工具.spec"
    echo ✅ 已删除spec文件
)

echo.
echo ============================================================
echo                        打包完成！
echo ============================================================
echo.

if exist "dist\Excel处理工具.exe" (
    echo ✅ 可执行文件已生成: dist\Excel处理工具.exe
    echo.
    echo 文件信息:
    for %%i in ("dist\Excel处理工具.exe") do echo    大小: %%~zi 字节 ^(约 %%~zi/1048576 MB^)
    echo    位置: %CD%\dist\Excel处理工具.exe
    echo.
    echo 📋 使用说明:
    echo 1. 将 'Excel处理工具.exe' 复制到目标电脑
    echo 2. 双击运行exe文件
    echo 3. 按照提示选择Excel文件和保存位置
    echo 4. 等待处理完成
    echo.
    echo 💡 提示:
    echo - 支持 .xlsx 和 .xls 格式的Excel文件
    echo - 程序会自动删除"预期评分"、"折扣"、"调整奖金"列
    echo - 所有公式会转换为数值
    echo - 原文件不会被修改
) else (
    echo ❌ 警告: 未找到生成的exe文件
    echo 请检查dist目录中的文件
    if exist dist (
        echo.
        echo dist目录内容:
        dir dist
    )
)

echo.
echo ============================================================
echo 按任意键退出...
pause >nul

('/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/PYZ-00.pyz',
 [('__future__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('base64',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/base64.py',
   'PYMODULE'),
  ('bdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bdb.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('cmd',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/cmd.py',
   'PYMODULE'),
  ('code',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copy.py',
   'PYMODULE'),
  ('csv',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('dis',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dis.py',
   'PYMODULE'),
  ('doctest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('email',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/et_xmlfile/xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('glob',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/glob.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('html',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('http',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('json',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/version.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/abc.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/product.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/singleton.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/slots.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/interface.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/cell.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/dataframe.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/inference.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/cell_watch.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/controls.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/errors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/ole.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/smart_tag.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/functions.py',
   'PYMODULE'),
  ('optparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('pandas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/__init__.py',
   'PYMODULE'),
  ('pandas._config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/localization.py',
   'PYMODULE'),
  ('pandas._libs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/__init__.py',
   'PYMODULE'),
  ('pandas._testing._hypothesis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_hypothesis.py',
   'PYMODULE'),
  ('pandas._testing._io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_typing.py',
   'PYMODULE'),
  ('pandas._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/extensions/__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/types/__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/typing/__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pyarrow.py',
   'PYMODULE'),
  ('pandas.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/conftest.py',
   'PYMODULE'),
  ('pandas.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/base.py',
   'PYMODULE'),
  ('pandas.core.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sorting.py',
   'PYMODULE'),
  ('pandas.core.sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.sparse.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sparse/api.py',
   'PYMODULE'),
  ('pandas.core.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/times.py',
   'PYMODULE'),
  ('pandas.core.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/errors/__init__.py',
   'PYMODULE'),
  ('pandas.io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboard/__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/html.py',
   'PYMODULE'),
  ('pandas.io.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/testing.py',
   'PYMODULE'),
  ('pandas.tests',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/__init__.py',
   'PYMODULE'),
  ('pandas.tests.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/api/__init__.py',
   'PYMODULE'),
  ('pandas.tests.api.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/api/test_api.py',
   'PYMODULE'),
  ('pandas.tests.api.test_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/api/test_types.py',
   'PYMODULE'),
  ('pandas.tests.apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/__init__.py',
   'PYMODULE'),
  ('pandas.tests.apply.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/common.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_frame_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply_relabeling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_frame_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_transform',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_frame_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_invalid_arg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_invalid_arg.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_numba.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_series_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply_relabeling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_series_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_transform',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_series_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_str',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_str.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/common.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/conftest.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_array_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_array_ops.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_datetime64',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_datetime64.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_object',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_object.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_period.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_timedelta64',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_timedelta64.py',
   'PYMODULE'),
  ('pandas.tests.arrays',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_comparison',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_logical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_logical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_ops.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_reduction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_algos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_algos.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_analytics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_api.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_map',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_map.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_missing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_operators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_operators.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_replace.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_sorting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_subclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_take.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_warnings.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_cumulative',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_comparison',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_contains',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_contains.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_to_numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_comparison',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_reduction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval_pyarrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_interval_pyarrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_overlaps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arrow_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_function',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked_shared',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked_shared.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/numpy_/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/numpy_/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/numpy_/test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_arrow_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_accessor.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_arithmetics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_arithmetics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_combine_concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_combine_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_dtype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_libsparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_libsparse.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_unary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_unary.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/test_string.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string_arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/test_string_arrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_ndarray_backed',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_ndarray_backed.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_period.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_cumulative',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/__init__.py',
   'PYMODULE'),
  ('pandas.tests.base.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/common.py',
   'PYMODULE'),
  ('pandas.tests.base.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.base.test_conversion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.base.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.base.test_misc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_misc.py',
   'PYMODULE'),
  ('pandas.tests.base.test_transpose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.base.test_unique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_unique.py',
   'PYMODULE'),
  ('pandas.tests.base.test_value_counts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.computation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/computation/__init__.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/computation/test_compat.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_eval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/computation/test_eval.py',
   'PYMODULE'),
  ('pandas.tests.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/config/__init__.py',
   'PYMODULE'),
  ('pandas.tests.config.test_config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/config/test_config.py',
   'PYMODULE'),
  ('pandas.tests.config.test_localization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/config/test_localization.py',
   'PYMODULE'),
  ('pandas.tests.construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/construction/__init__.py',
   'PYMODULE'),
  ('pandas.tests.construction.test_extract_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/construction/test_extract_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_datetimeindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_datetimeindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_index.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_periodindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_periodindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_timedeltaindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_timedeltaindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_chained_assignment_deprecation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_chained_assignment_deprecation.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_clip',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_clip.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_core_functionalities',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_core_functionalities.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_functions.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_internals.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_interp_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_interp_fillna.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_methods.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_replace.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_setitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_util.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/util.py',
   'PYMODULE'),
  ('pandas.tests.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_can_hold_element',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_can_hold_element.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_from_scalar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_construct_from_scalar.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_ndarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_construct_ndarray.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_object_arr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_construct_object_arr.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_dict_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_dict_compat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_downcast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_downcast.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_find_common_type',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_find_common_type.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_infer_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_infer_dtype.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_maybe_box_native',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_maybe_box_native.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_promote',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_promote.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_common.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_concat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_generic.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_inference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_inference.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_missing.py',
   'PYMODULE'),
  ('pandas.tests.extension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/array_with_attr/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/array_with_attr/array.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.test_array_with_attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/array_with_attr/test_array_with_attr.py',
   'PYMODULE'),
  ('pandas.tests.extension.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.accumulate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/accumulate.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.casting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/casting.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/constructors.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dim2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/dim2.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/dtype.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.getitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/getitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/groupby.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/index.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.interface',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/interface.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/io.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/methods.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/missing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/ops.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.printing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/printing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reduce',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/reduce.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reshaping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/reshaping.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.setitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/setitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/conftest.py',
   'PYMODULE'),
  ('pandas.tests.extension.date',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/date/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.date.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/date/array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/decimal/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/decimal/array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.test_decimal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/decimal/test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.extension.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/json/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/json/array.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.test_json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/json/test_json.py',
   'PYMODULE'),
  ('pandas.tests.extension.list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/list/__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/list/array.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.test_list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/list/test_list.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_arrow.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_common.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_extension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_extension.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_interval.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_masked',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_masked.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_period.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_sparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_sparse.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_string.py',
   'PYMODULE'),
  ('pandas.tests.frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/common.py',
   'PYMODULE'),
  ('pandas.tests.frame.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/constructors/__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_dict',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/constructors/test_from_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_records',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/constructors/test_from_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_coercion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_delitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_get.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get_value',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_get_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_getitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_insert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_insert.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_mask',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_mask.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_set_value',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_setitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_take.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_where',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_where.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_xs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_xs.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_add_prefix_suffix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_align',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_align.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asfreq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asof',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_asof.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_assign',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_assign.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_at_time',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_at_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_between_time',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_between_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_clip',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_clip.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_combine.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine_first',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_compare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_compare.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_convert_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_copy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_copy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_count',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_count.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_cov_corr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_describe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_describe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_diff',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_diff.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_dot.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_drop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop_duplicates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_droplevel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_droplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dropna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_duplicated',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_equals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_equals.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_explode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_explode.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_filter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_filter.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_and_last',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_first_and_last.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_valid_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_first_valid_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_get_numeric_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_head_tail',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_infer_objects',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_info',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_info.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_interpolate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_is_homogeneous_dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_is_homogeneous_dtype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isetitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_isetitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_isin.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_iterrows',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_iterrows.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_join.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_map',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_map.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_matmul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_nlargest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pct_change',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pipe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_pop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rank',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_rank.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_rename.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename_axis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reorder_levels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reorder_levels.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_replace.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reset_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_round',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_round.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_sample.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_select_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_select_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_axis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_set_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_set_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_shift',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_shift.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_size',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_size.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swapaxes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_swapaxes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swaplevel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_swaplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_csv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict_of_blocks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_dict_of_blocks.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_records',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_timestamp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_transpose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_truncate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_convert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_localize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_update',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_update.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_value_counts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_alter_axes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_alter_axes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_api.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arrow_interface',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_arrow_interface.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_block_internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_block_internals.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_cumulative',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_iteration',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_logical_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_nonunique_indexes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_nonunique_indexes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_npfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_query_eval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_query_eval.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_repr.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_stack_unstack',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_stack_unstack.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_subclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_ufunc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_unary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_unary.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_validate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_validate.py',
   'PYMODULE'),
  ('pandas.tests.generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/__init__.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_duplicate_labels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_duplicate_labels.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_finalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_finalize.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_frame.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_generic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_generic.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_label_or_level_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_label_or_level_utils.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_series.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_to_xarray',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_to_xarray.py',
   'PYMODULE'),
  ('pandas.tests.groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_aggregate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_aggregate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_cython',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_cython.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_other',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_other.py',
   'PYMODULE'),
  ('pandas.tests.groupby.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/conftest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_corrwith',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_corrwith.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_describe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_describe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_groupby_shift_diff',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_groupby_shift_diff.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_is_monotonic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nlargest_nsmallest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_nlargest_nsmallest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_nth.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_rank',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_rank.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_sample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_sample.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_size',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_size.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_skew',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_skew.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_value_counts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_all_methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_all_methods.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_api.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_apply.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply_mutate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_apply_mutate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_bin_groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_bin_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_counting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_counting.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_cumulative',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_filters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_filters.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_dropna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_groupby_dropna.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_subclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_groupby_subclass.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_grouping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_grouping.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_index_as_string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_libgroupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_libgroupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_missing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numeric_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_numeric_only.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_pipe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_raises',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_raises.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_timegrouper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_timegrouper.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/transform/__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/transform/test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_transform',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/transform/test_transform.py',
   'PYMODULE'),
  ('pandas.tests.indexes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_where',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_where.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_append',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_append.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_category',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_category.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_equals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_map',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_reindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_drop_duplicates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_equals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_is_monotonic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_nat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_nat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_sort_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_value_counts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_asof',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_asof.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_delete',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_factorize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_insert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_isocalendar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_isocalendar.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_map',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_repeat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_resolution',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_round',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_round.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_shift',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_snap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_snap.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_julian_date',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_pydatetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_series.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_convert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_localize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_unique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_unique.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_date_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_date_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_freq_attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_iter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_iter.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_npfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_partial_slicing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_reindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_scalar_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_timezones',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_equals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_interval_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_tree',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_interval_tree.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_analytics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_conversion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_copy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_copy.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_drop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_drop.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_duplicates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_equivalence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_equivalence.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_level_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_get_level_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_set',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_get_set.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_integrity',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_integrity.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_isin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_isin.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_lexsort',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_lexsort.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_missing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_monotonic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_names',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_names.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_partial_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_partial_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_sorting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_take.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/object/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/object/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/object/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_asfreq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_factorize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_insert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_is_full',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_is_full.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_repeat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_shift',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_to_timestamp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_freq_attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_monotonic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_partial_slicing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_period_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_resolution',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_scalar_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_searchsorted',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_tools.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/string/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.string.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/string/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.string.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/string/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_any_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_any_index.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_engines',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_engines.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_frozen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_frozen.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_index_new',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_index_new.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_numpy_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_numpy_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_old_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_old_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_subclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_factorize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_insert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_repeat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_shift',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_delete',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_freq_attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_scalar_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_searchsorted',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_setops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta_range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_timedelta_range.py',
   'PYMODULE'),
  ('pandas.tests.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/common.py',
   'PYMODULE'),
  ('pandas.tests.indexing.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/interval/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/interval/test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval_new',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/interval/test_interval_new.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_chaining_and_caching',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_getitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_iloc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_indexing_slow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_indexing_slow.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_loc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_multiindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_multiindex.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_partial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_setitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_slice',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_slice.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_sorted',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_sorted.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_at',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_at.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_chaining_and_caching',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_check_indexer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_check_indexer.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_coercion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_floats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_floats.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_iat.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iloc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_indexers.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_loc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_na_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_na_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_partial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_scalar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_scalar.py',
   'PYMODULE'),
  ('pandas.tests.interchange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/test_impl.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_spec_conformance',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/test_spec_conformance.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/test_utils.py',
   'PYMODULE'),
  ('pandas.tests.internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/__init__.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/test_api.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/test_internals.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_managers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/test_managers.py',
   'PYMODULE'),
  ('pandas.tests.io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_odf.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odswriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_odswriter.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_openpyxl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_openpyxl.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_readers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_readers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_writers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_writers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlrd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_xlrd.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlsxwriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.tests.io.formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_bar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_bar.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_exceptions.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_highlight',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_highlight.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_matplotlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_matplotlib.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_non_unique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_non_unique.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_latex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_tooltip',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_tooltip.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_console.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_css',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_css.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_eng_formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_eng_formatting.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_ipython_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_ipython_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_printing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_printing.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_csv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_excel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_excel.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_latex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_markdown',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_markdown.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.generate_legacy_storage_files',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/generate_legacy_storage_files.py',
   'PYMODULE'),
  ('pandas.tests.io.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.json.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_compression',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_deprecated_kwargs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_deprecated_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_json_table_schema.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema_ext_dtype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_json_table_schema_ext_dtype.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_pandas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_pandas.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_readlines',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_readlines.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_ujson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_ujson.py',
   'PYMODULE'),
  ('pandas.tests.io.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_chunksize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_chunksize.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_common_basic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_common_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_data_list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_data_list.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_decimal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_file_buffer_url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_file_buffer_url.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_float',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_float.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_index.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_inf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_inf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_ints',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_ints.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_iterator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_iterator.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_read_errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_read_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_verbose',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_verbose.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_dtypes_basic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/test_dtypes_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_empty',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/test_empty.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_c_parser_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_c_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_comment',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_comment.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_compression',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_concatenate_chunks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_concatenate_chunks.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_converters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_converters.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_dialect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_dialect.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_encoding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_encoding.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_header',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_header.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_index_col',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_index_col.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_mangle_dupes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_mangle_dupes.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_multi_thread',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_multi_thread.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_na_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_na_values.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_network',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_network.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_parse_dates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_python_parser_only',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_python_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_quoting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_quoting.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_read_fwf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_read_fwf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_skiprows',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_skiprows.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_textreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_textreader.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_unsupported',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_unsupported.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_upcast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_upcast.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_parse_dates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/test_strings.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_usecols_basic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/test_usecols_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/common.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_append',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_append.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_complex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_complex.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_file_handling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_file_handling.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_keys',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_keys.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_put',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_put.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_pytables_missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_pytables_missing.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_read',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_read.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_retain_attributes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_retain_attributes.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_round_trip',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_round_trip.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_select',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_select.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_store',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_store.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_subclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_time_series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_time_series.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_timezones',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.io.sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_byteswap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_byteswap.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_sas.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas7bdat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_sas7bdat.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_xport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_xport.py',
   'PYMODULE'),
  ('pandas.tests.io.test_clipboard',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_clipboard.py',
   'PYMODULE'),
  ('pandas.tests.io.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_common.py',
   'PYMODULE'),
  ('pandas.tests.io.test_compression',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.test_feather',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_feather.py',
   'PYMODULE'),
  ('pandas.tests.io.test_fsspec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_fsspec.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gbq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_gbq.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gcs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_gcs.py',
   'PYMODULE'),
  ('pandas.tests.io.test_html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.test_http_headers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_http_headers.py',
   'PYMODULE'),
  ('pandas.tests.io.test_orc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_orc.py',
   'PYMODULE'),
  ('pandas.tests.io.test_parquet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_parquet.py',
   'PYMODULE'),
  ('pandas.tests.io.test_pickle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.io.test_s3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_s3.py',
   'PYMODULE'),
  ('pandas.tests.io.test_spss',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_spss.py',
   'PYMODULE'),
  ('pandas.tests.io.test_sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_sql.py',
   'PYMODULE'),
  ('pandas.tests.io.test_stata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_stata.py',
   'PYMODULE'),
  ('pandas.tests.io.xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_to_xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/test_to_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/test_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/test_xml_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.libs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/__init__.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_hashtable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_hashtable.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_join.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_lib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_lib.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_libalgos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_libalgos.py',
   'PYMODULE'),
  ('pandas.tests.plotting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/conftest.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_color',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_color.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_legend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_legend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_subplots',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_subplots.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_hist_box_by',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_hist_box_by.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_backend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_boxplot_method',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_boxplot_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_converter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_converter.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_datetimelike',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_hist_method',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_hist_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_misc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_misc.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_series.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_style.py',
   'PYMODULE'),
  ('pandas.tests.reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reductions/__init__.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reductions/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_stat_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reductions/test_stat_reductions.py',
   'PYMODULE'),
  ('pandas.tests.resample',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/__init__.py',
   'PYMODULE'),
  ('pandas.tests.resample.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/conftest.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_base.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_datetime_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_datetime_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_period_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_period_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resample_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_resample_api.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resampler_grouper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_resampler_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_time_grouper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_time_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_timedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.reshape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/conftest.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_append.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_append_common.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_categorical',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_concat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_concat.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_dataframe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_dataframe.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_datetimes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_empty',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_empty.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_index.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_invalid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_invalid.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_series.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_sort',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_sort.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_join.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_asof',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_asof.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_cross',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_cross.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_index_as_string',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_ordered',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_ordered.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_multi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_multi.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_crosstab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_crosstab.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_cut',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_cut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_from_dummies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_from_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_get_dummies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_melt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_melt.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_pivot.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot_multilevel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_pivot_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_qcut',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_qcut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_union_categoricals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_union_categoricals.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_util.py',
   'PYMODULE'),
  ('pandas.tests.scalar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_contains',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_contains.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_interval',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_interval.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_overlaps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_asfreq',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/test_period.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_na_scalar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/test_na_scalar.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_nat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/test_nat.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_as_unit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/methods/test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_round',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/methods/test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_timedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_as_unit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_replace.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_round',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_timestamp_method',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_timestamp_method.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_julian_date',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_pydatetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_convert',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_localize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_comparisons',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_comparisons.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timestamp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timezones',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.series',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_cat_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_cat_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_dt_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_dt_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_list_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_list_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_sparse_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_sparse_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_str_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_str_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_struct_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_struct_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_delitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_get',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_get.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_getitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_indexing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_mask',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_mask.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_set_value',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_setitem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_take.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_where',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_where.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_xs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_xs.py',
   'PYMODULE'),
  ('pandas.tests.series.methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_add_prefix_suffix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_align',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_align.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_argsort',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_argsort.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_asof',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_asof.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_astype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_astype.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_autocorr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_autocorr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_between',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_between.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_case_when',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_case_when.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_clip',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_clip.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_combine.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine_first',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_compare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_compare.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_convert_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_copy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_copy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_count',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_count.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_cov_corr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_describe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_describe.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_diff',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_diff.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_drop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop_duplicates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dropna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_duplicated',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_equals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_equals.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_explode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_explode.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_fillna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_get_numeric_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_head_tail',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_infer_objects',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_info',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_info.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_interpolate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_monotonic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_unique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_is_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_isin.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_isna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_item',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_item.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_map',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_map.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_matmul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nlargest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nunique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_nunique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pct_change',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_pop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rank',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_rank.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_rename.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename_axis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_repeat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_replace.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reset_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_round',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_round.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_searchsorted',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_set_name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_set_name.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_size',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_size.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_csv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_dict',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_frame',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_numpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tolist',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_tolist.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_truncate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tz_localize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unstack',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_unstack.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_update',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_update.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_value_counts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_values',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_view',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_view.py',
   'PYMODULE'),
  ('pandas.tests.series.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_api.py',
   'PYMODULE'),
  ('pandas.tests.series.test_arithmetic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.series.test_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.series.test_cumulative',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.series.test_formats',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_formats.py',
   'PYMODULE'),
  ('pandas.tests.series.test_iteration',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.series.test_logical_ops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.series.test_missing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_missing.py',
   'PYMODULE'),
  ('pandas.tests.series.test_npfuncs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.series.test_reductions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.series.test_subclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.series.test_ufunc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.series.test_unary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_unary.py',
   'PYMODULE'),
  ('pandas.tests.series.test_validate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_validate.py',
   'PYMODULE'),
  ('pandas.tests.strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/conftest.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_api.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_case_justify',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_case_justify.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_cat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_cat.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_extract',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_extract.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_find_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_find_replace.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_get_dummies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_split_partition',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_split_partition.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_string_array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_string_array.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_strings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_strings.py',
   'PYMODULE'),
  ('pandas.tests.test_aggregation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_aggregation.py',
   'PYMODULE'),
  ('pandas.tests.test_algos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_algos.py',
   'PYMODULE'),
  ('pandas.tests.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_common.py',
   'PYMODULE'),
  ('pandas.tests.test_downstream',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_downstream.py',
   'PYMODULE'),
  ('pandas.tests.test_errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_errors.py',
   'PYMODULE'),
  ('pandas.tests.test_expressions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_expressions.py',
   'PYMODULE'),
  ('pandas.tests.test_flags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_flags.py',
   'PYMODULE'),
  ('pandas.tests.test_multilevel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.test_nanops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_nanops.py',
   'PYMODULE'),
  ('pandas.tests.test_optional_dependency',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_optional_dependency.py',
   'PYMODULE'),
  ('pandas.tests.test_register_accessor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_register_accessor.py',
   'PYMODULE'),
  ('pandas.tests.test_sorting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.test_take',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_take.py',
   'PYMODULE'),
  ('pandas.tests.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/__init__.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_numeric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_numeric.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_time',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_time.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_timedelta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.tseries',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_freq_code',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/test_freq_code.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_frequencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/test_frequencies.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_inference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/test_inference.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_calendar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_calendar.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_federal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_federal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_holiday',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_holiday.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_observance',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_observance.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_day',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_hour',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_month',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_quarter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_year',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_year.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_day',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_custom_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_hour',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_custom_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_month',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_custom_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_dst',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_dst.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_easter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_fiscal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_fiscal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_index',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_index.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_month',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_offsets.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets_properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_offsets_properties.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_quarter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_ticks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_ticks.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_week',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_week.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_year',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_year.py',
   'PYMODULE'),
  ('pandas.tests.tslibs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_api.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_array_to_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_array_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_ccalendar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_ccalendar.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_conversion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_fields.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_libfrequencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_libfrequencies.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_liboffsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_liboffsets.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_np_datetime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_np_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_npy_units',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_npy_units.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parse_iso8601',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_parse_iso8601.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parsing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_parsing.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_period',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_period.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_resolution',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_strptime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_strptime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timedeltas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timezones',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_to_offset',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_to_offset.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_tzconversion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_tzconversion.py',
   'PYMODULE'),
  ('pandas.tests.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/conftest.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_almost_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_almost_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_attr_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_attr_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_categorical_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_categorical_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_extension_array_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_extension_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_frame_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_frame_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_index_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_index_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_interval_array_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_interval_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_numpy_array_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_numpy_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_produces_warning',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_produces_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_series_equal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_series_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_deprecate.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_kwarg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_deprecate_kwarg.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_nonkeyword_arguments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_deprecate_nonkeyword_arguments.py',
   'PYMODULE'),
  ('pandas.tests.util.test_doc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_doc.py',
   'PYMODULE'),
  ('pandas.tests.util.test_hashing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_hashing.py',
   'PYMODULE'),
  ('pandas.tests.util.test_numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_numba.py',
   'PYMODULE'),
  ('pandas.tests.util.test_rewrite_warning',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_rewrite_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_shares_memory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_shares_memory.py',
   'PYMODULE'),
  ('pandas.tests.util.test_show_versions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_show_versions.py',
   'PYMODULE'),
  ('pandas.tests.util.test_util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_util.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_args.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args_and_kwargs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_args_and_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_inclusive',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_inclusive.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_kwargs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.conftest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_ewm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/test_moments_consistency_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_expanding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/test_moments_consistency_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_rolling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/test_moments_consistency_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_api.py',
   'PYMODULE'),
  ('pandas.tests.window.test_apply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_apply.py',
   'PYMODULE'),
  ('pandas.tests.window.test_base_indexer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_base_indexer.py',
   'PYMODULE'),
  ('pandas.tests.window.test_cython_aggregations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_cython_aggregations.py',
   'PYMODULE'),
  ('pandas.tests.window.test_dtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.window.test_ewm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.test_expanding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.test_groupby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.window.test_numba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_numba.py',
   'PYMODULE'),
  ('pandas.tests.window.test_online',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_online.py',
   'PYMODULE'),
  ('pandas.tests.window.test_pairwise',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_pairwise.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling_functions.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_quantile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling_quantile.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_skew_kurt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling_skew_kurt.py',
   'PYMODULE'),
  ('pandas.tests.window.test_timeseries_window',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_timeseries_window.py',
   'PYMODULE'),
  ('pandas.tests.window.test_win_type',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_win_type.py',
   'PYMODULE'),
  ('pandas.tseries',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/offsets.py',
   'PYMODULE'),
  ('pandas.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_decorators.py',
   'PYMODULE'),
  ('pandas.util._doctools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_doctools.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_print_versions.py',
   'PYMODULE'),
  ('pandas.util._test_decorators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_test_decorators.py',
   'PYMODULE'),
  ('pandas.util._tester',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/version/__init__.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('pdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pdb.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pickletools',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pytz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/tzinfo.py',
   'PYMODULE'),
  ('queue',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/random.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/signal.py',
   'PYMODULE'),
  ('six',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/six.py',
   'PYMODULE'),
  ('socket',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dump.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/threading.py',
   'PYMODULE'),
  ('tkinter',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tkinter/simpledialog.py',
   'PYMODULE'),
  ('token',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('tty',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tty.py',
   'PYMODULE'),
  ('typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/typing.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('uu',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uu.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uuid.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.dom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zoneinfo/__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zoneinfo/_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zoneinfo/_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zoneinfo/_zoneinfo.py',
   'PYMODULE')])

('/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/Excel处理工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('process_excel',
   '/Users/<USER>/Data/WorkSpace/python/excel/process_excel.py',
   'PYSOURCE'),
  ('pandas/_libs/lib.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/lib.cpython-39-darwin.so',
   'BINARY'),
  ('Python3.framework/Versions/3.9/Python3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3',
   'BINARY'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_elementtree.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_tests.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/_core/_multiarray_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/linalg/_umath_linalg.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/mtrand.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_sfc64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_philox.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_pcg64.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_mt19937.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/bit_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_generator.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_bounded_integers.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/random/_common.cpython-39-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/numpy/fft/_pocketfft_umath.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_zoneinfo.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_zoneinfo.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_uuid.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sqlite3.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/writers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/indexers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/aggregations.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/vectorized.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/tzconversion.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timezones.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timestamps.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timedeltas.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/strptime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/period.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/parsing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/offsets.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/np_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/nattype.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/fields.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/dtypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/conversion.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/ccalendar.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/base.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslib.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/testing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sparse.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sas.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/reshape.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/properties.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/parsers.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/pandas_parser.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/pandas_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops_dispatch.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/missing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/json.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/join.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/interval.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/internals.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/indexing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/index.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashtable.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashing.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/groupby.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/byteswap.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/arrays.cpython-39-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-39-darwin.so',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/algos.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_tkinter.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/cmath.cpython-39-darwin.so',
   'EXTENSION'),
  ('openpyxl-3.1.5.dist-info/INSTALLER',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/INSTALLER',
   'DATA'),
  ('openpyxl-3.1.5.dist-info/LICENCE.rst',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/LICENCE.rst',
   'DATA'),
  ('openpyxl-3.1.5.dist-info/METADATA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/METADATA',
   'DATA'),
  ('openpyxl-3.1.5.dist-info/RECORD',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/RECORD',
   'DATA'),
  ('openpyxl-3.1.5.dist-info/REQUESTED',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/REQUESTED',
   'DATA'),
  ('openpyxl-3.1.5.dist-info/WHEEL',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/WHEEL',
   'DATA'),
  ('openpyxl-3.1.5.dist-info/top_level.txt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl-3.1.5.dist-info/top_level.txt',
   'DATA'),
  ('openpyxl/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/__init__.py',
   'DATA'),
  ('openpyxl/_constants.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/_constants.py',
   'DATA'),
  ('openpyxl/cell/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/__init__.py',
   'DATA'),
  ('openpyxl/cell/_writer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/_writer.py',
   'DATA'),
  ('openpyxl/cell/cell.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/cell.py',
   'DATA'),
  ('openpyxl/cell/read_only.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/read_only.py',
   'DATA'),
  ('openpyxl/cell/rich_text.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/rich_text.py',
   'DATA'),
  ('openpyxl/cell/text.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/cell/text.py',
   'DATA'),
  ('openpyxl/chart/_3d.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_3d.py',
   'DATA'),
  ('openpyxl/chart/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/__init__.py',
   'DATA'),
  ('openpyxl/chart/_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/_chart.py',
   'DATA'),
  ('openpyxl/chart/area_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/area_chart.py',
   'DATA'),
  ('openpyxl/chart/axis.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/axis.py',
   'DATA'),
  ('openpyxl/chart/bar_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bar_chart.py',
   'DATA'),
  ('openpyxl/chart/bubble_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/bubble_chart.py',
   'DATA'),
  ('openpyxl/chart/chartspace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/chartspace.py',
   'DATA'),
  ('openpyxl/chart/data_source.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/data_source.py',
   'DATA'),
  ('openpyxl/chart/descriptors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/descriptors.py',
   'DATA'),
  ('openpyxl/chart/error_bar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/error_bar.py',
   'DATA'),
  ('openpyxl/chart/label.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/label.py',
   'DATA'),
  ('openpyxl/chart/layout.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/layout.py',
   'DATA'),
  ('openpyxl/chart/legend.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/legend.py',
   'DATA'),
  ('openpyxl/chart/line_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/line_chart.py',
   'DATA'),
  ('openpyxl/chart/marker.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/marker.py',
   'DATA'),
  ('openpyxl/chart/picture.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/picture.py',
   'DATA'),
  ('openpyxl/chart/pie_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pie_chart.py',
   'DATA'),
  ('openpyxl/chart/pivot.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/pivot.py',
   'DATA'),
  ('openpyxl/chart/plotarea.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/plotarea.py',
   'DATA'),
  ('openpyxl/chart/print_settings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/print_settings.py',
   'DATA'),
  ('openpyxl/chart/radar_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/radar_chart.py',
   'DATA'),
  ('openpyxl/chart/reader.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reader.py',
   'DATA'),
  ('openpyxl/chart/reference.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/reference.py',
   'DATA'),
  ('openpyxl/chart/scatter_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/scatter_chart.py',
   'DATA'),
  ('openpyxl/chart/series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series.py',
   'DATA'),
  ('openpyxl/chart/series_factory.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/series_factory.py',
   'DATA'),
  ('openpyxl/chart/shapes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/shapes.py',
   'DATA'),
  ('openpyxl/chart/stock_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/stock_chart.py',
   'DATA'),
  ('openpyxl/chart/surface_chart.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/surface_chart.py',
   'DATA'),
  ('openpyxl/chart/text.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/text.py',
   'DATA'),
  ('openpyxl/chart/title.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/title.py',
   'DATA'),
  ('openpyxl/chart/trendline.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/trendline.py',
   'DATA'),
  ('openpyxl/chart/updown_bars.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chart/updown_bars.py',
   'DATA'),
  ('openpyxl/chartsheet/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/__init__.py',
   'DATA'),
  ('openpyxl/chartsheet/chartsheet.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/chartsheet.py',
   'DATA'),
  ('openpyxl/chartsheet/custom.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/custom.py',
   'DATA'),
  ('openpyxl/chartsheet/properties.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/properties.py',
   'DATA'),
  ('openpyxl/chartsheet/protection.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/protection.py',
   'DATA'),
  ('openpyxl/chartsheet/publish.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/publish.py',
   'DATA'),
  ('openpyxl/chartsheet/relation.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/relation.py',
   'DATA'),
  ('openpyxl/chartsheet/views.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/chartsheet/views.py',
   'DATA'),
  ('openpyxl/comments/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/__init__.py',
   'DATA'),
  ('openpyxl/comments/author.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/author.py',
   'DATA'),
  ('openpyxl/comments/comment_sheet.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comment_sheet.py',
   'DATA'),
  ('openpyxl/comments/comments.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/comments.py',
   'DATA'),
  ('openpyxl/comments/shape_writer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/comments/shape_writer.py',
   'DATA'),
  ('openpyxl/compat/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/__init__.py',
   'DATA'),
  ('openpyxl/compat/abc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/abc.py',
   'DATA'),
  ('openpyxl/compat/numbers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/numbers.py',
   'DATA'),
  ('openpyxl/compat/product.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/product.py',
   'DATA'),
  ('openpyxl/compat/singleton.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/singleton.py',
   'DATA'),
  ('openpyxl/compat/strings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/compat/strings.py',
   'DATA'),
  ('openpyxl/descriptors/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/__init__.py',
   'DATA'),
  ('openpyxl/descriptors/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/base.py',
   'DATA'),
  ('openpyxl/descriptors/container.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/container.py',
   'DATA'),
  ('openpyxl/descriptors/excel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/excel.py',
   'DATA'),
  ('openpyxl/descriptors/namespace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/namespace.py',
   'DATA'),
  ('openpyxl/descriptors/nested.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/nested.py',
   'DATA'),
  ('openpyxl/descriptors/sequence.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/sequence.py',
   'DATA'),
  ('openpyxl/descriptors/serialisable.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/serialisable.py',
   'DATA'),
  ('openpyxl/descriptors/slots.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/descriptors/slots.py',
   'DATA'),
  ('openpyxl/drawing/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/__init__.py',
   'DATA'),
  ('openpyxl/drawing/colors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/colors.py',
   'DATA'),
  ('openpyxl/drawing/connector.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/connector.py',
   'DATA'),
  ('openpyxl/drawing/drawing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/drawing.py',
   'DATA'),
  ('openpyxl/drawing/effect.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/effect.py',
   'DATA'),
  ('openpyxl/drawing/fill.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/fill.py',
   'DATA'),
  ('openpyxl/drawing/geometry.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/geometry.py',
   'DATA'),
  ('openpyxl/drawing/graphic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/graphic.py',
   'DATA'),
  ('openpyxl/drawing/image.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/image.py',
   'DATA'),
  ('openpyxl/drawing/line.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/line.py',
   'DATA'),
  ('openpyxl/drawing/picture.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/picture.py',
   'DATA'),
  ('openpyxl/drawing/properties.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/properties.py',
   'DATA'),
  ('openpyxl/drawing/relation.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/relation.py',
   'DATA'),
  ('openpyxl/drawing/spreadsheet_drawing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/spreadsheet_drawing.py',
   'DATA'),
  ('openpyxl/drawing/text.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/text.py',
   'DATA'),
  ('openpyxl/drawing/xdr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/drawing/xdr.py',
   'DATA'),
  ('openpyxl/formatting/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/__init__.py',
   'DATA'),
  ('openpyxl/formatting/formatting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/formatting.py',
   'DATA'),
  ('openpyxl/formatting/rule.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formatting/rule.py',
   'DATA'),
  ('openpyxl/formula/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/__init__.py',
   'DATA'),
  ('openpyxl/formula/tokenizer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/tokenizer.py',
   'DATA'),
  ('openpyxl/formula/translate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/formula/translate.py',
   'DATA'),
  ('openpyxl/packaging/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/__init__.py',
   'DATA'),
  ('openpyxl/packaging/core.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/core.py',
   'DATA'),
  ('openpyxl/packaging/custom.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/custom.py',
   'DATA'),
  ('openpyxl/packaging/extended.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/extended.py',
   'DATA'),
  ('openpyxl/packaging/interface.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/interface.py',
   'DATA'),
  ('openpyxl/packaging/manifest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/manifest.py',
   'DATA'),
  ('openpyxl/packaging/relationship.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/relationship.py',
   'DATA'),
  ('openpyxl/packaging/workbook.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/packaging/workbook.py',
   'DATA'),
  ('openpyxl/pivot/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/__init__.py',
   'DATA'),
  ('openpyxl/pivot/cache.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/cache.py',
   'DATA'),
  ('openpyxl/pivot/fields.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/fields.py',
   'DATA'),
  ('openpyxl/pivot/record.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/record.py',
   'DATA'),
  ('openpyxl/pivot/table.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/pivot/table.py',
   'DATA'),
  ('openpyxl/reader/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/__init__.py',
   'DATA'),
  ('openpyxl/reader/drawings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/drawings.py',
   'DATA'),
  ('openpyxl/reader/excel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/excel.py',
   'DATA'),
  ('openpyxl/reader/strings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/strings.py',
   'DATA'),
  ('openpyxl/reader/workbook.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/reader/workbook.py',
   'DATA'),
  ('openpyxl/styles/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/__init__.py',
   'DATA'),
  ('openpyxl/styles/alignment.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/alignment.py',
   'DATA'),
  ('openpyxl/styles/borders.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/borders.py',
   'DATA'),
  ('openpyxl/styles/builtins.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/builtins.py',
   'DATA'),
  ('openpyxl/styles/cell_style.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/cell_style.py',
   'DATA'),
  ('openpyxl/styles/colors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/colors.py',
   'DATA'),
  ('openpyxl/styles/differential.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/differential.py',
   'DATA'),
  ('openpyxl/styles/fills.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fills.py',
   'DATA'),
  ('openpyxl/styles/fonts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/fonts.py',
   'DATA'),
  ('openpyxl/styles/named_styles.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/named_styles.py',
   'DATA'),
  ('openpyxl/styles/numbers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/numbers.py',
   'DATA'),
  ('openpyxl/styles/protection.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/protection.py',
   'DATA'),
  ('openpyxl/styles/proxy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/proxy.py',
   'DATA'),
  ('openpyxl/styles/styleable.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/styleable.py',
   'DATA'),
  ('openpyxl/styles/stylesheet.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/stylesheet.py',
   'DATA'),
  ('openpyxl/styles/table.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/styles/table.py',
   'DATA'),
  ('openpyxl/utils/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/__init__.py',
   'DATA'),
  ('openpyxl/utils/bound_dictionary.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/bound_dictionary.py',
   'DATA'),
  ('openpyxl/utils/cell.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/cell.py',
   'DATA'),
  ('openpyxl/utils/dataframe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/dataframe.py',
   'DATA'),
  ('openpyxl/utils/datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/datetime.py',
   'DATA'),
  ('openpyxl/utils/escape.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/escape.py',
   'DATA'),
  ('openpyxl/utils/exceptions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/exceptions.py',
   'DATA'),
  ('openpyxl/utils/formulas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/formulas.py',
   'DATA'),
  ('openpyxl/utils/indexed_list.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/indexed_list.py',
   'DATA'),
  ('openpyxl/utils/inference.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/inference.py',
   'DATA'),
  ('openpyxl/utils/protection.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/protection.py',
   'DATA'),
  ('openpyxl/utils/units.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/utils/units.py',
   'DATA'),
  ('openpyxl/workbook/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/__init__.py',
   'DATA'),
  ('openpyxl/workbook/_writer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/_writer.py',
   'DATA'),
  ('openpyxl/workbook/child.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/child.py',
   'DATA'),
  ('openpyxl/workbook/defined_name.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/defined_name.py',
   'DATA'),
  ('openpyxl/workbook/external_link/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/__init__.py',
   'DATA'),
  ('openpyxl/workbook/external_link/external.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_link/external.py',
   'DATA'),
  ('openpyxl/workbook/external_reference.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/external_reference.py',
   'DATA'),
  ('openpyxl/workbook/function_group.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/function_group.py',
   'DATA'),
  ('openpyxl/workbook/properties.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/properties.py',
   'DATA'),
  ('openpyxl/workbook/protection.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/protection.py',
   'DATA'),
  ('openpyxl/workbook/smart_tags.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/smart_tags.py',
   'DATA'),
  ('openpyxl/workbook/views.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/views.py',
   'DATA'),
  ('openpyxl/workbook/web.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/web.py',
   'DATA'),
  ('openpyxl/workbook/workbook.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/workbook/workbook.py',
   'DATA'),
  ('openpyxl/worksheet/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/__init__.py',
   'DATA'),
  ('openpyxl/worksheet/_read_only.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_read_only.py',
   'DATA'),
  ('openpyxl/worksheet/_reader.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_reader.py',
   'DATA'),
  ('openpyxl/worksheet/_write_only.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_write_only.py',
   'DATA'),
  ('openpyxl/worksheet/_writer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/_writer.py',
   'DATA'),
  ('openpyxl/worksheet/cell_range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/cell_range.py',
   'DATA'),
  ('openpyxl/worksheet/cell_watch.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/cell_watch.py',
   'DATA'),
  ('openpyxl/worksheet/controls.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/controls.py',
   'DATA'),
  ('openpyxl/worksheet/copier.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/copier.py',
   'DATA'),
  ('openpyxl/worksheet/custom.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/custom.py',
   'DATA'),
  ('openpyxl/worksheet/datavalidation.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/datavalidation.py',
   'DATA'),
  ('openpyxl/worksheet/dimensions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/dimensions.py',
   'DATA'),
  ('openpyxl/worksheet/drawing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/drawing.py',
   'DATA'),
  ('openpyxl/worksheet/errors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/errors.py',
   'DATA'),
  ('openpyxl/worksheet/filters.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/filters.py',
   'DATA'),
  ('openpyxl/worksheet/formula.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/formula.py',
   'DATA'),
  ('openpyxl/worksheet/header_footer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/header_footer.py',
   'DATA'),
  ('openpyxl/worksheet/hyperlink.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/hyperlink.py',
   'DATA'),
  ('openpyxl/worksheet/merge.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/merge.py',
   'DATA'),
  ('openpyxl/worksheet/ole.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/ole.py',
   'DATA'),
  ('openpyxl/worksheet/page.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/page.py',
   'DATA'),
  ('openpyxl/worksheet/pagebreak.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/pagebreak.py',
   'DATA'),
  ('openpyxl/worksheet/picture.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/picture.py',
   'DATA'),
  ('openpyxl/worksheet/print_settings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/print_settings.py',
   'DATA'),
  ('openpyxl/worksheet/properties.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/properties.py',
   'DATA'),
  ('openpyxl/worksheet/protection.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/protection.py',
   'DATA'),
  ('openpyxl/worksheet/related.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/related.py',
   'DATA'),
  ('openpyxl/worksheet/scenario.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/scenario.py',
   'DATA'),
  ('openpyxl/worksheet/smart_tag.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/smart_tag.py',
   'DATA'),
  ('openpyxl/worksheet/table.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/table.py',
   'DATA'),
  ('openpyxl/worksheet/views.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/views.py',
   'DATA'),
  ('openpyxl/worksheet/worksheet.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/worksheet/worksheet.py',
   'DATA'),
  ('openpyxl/writer/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/__init__.py',
   'DATA'),
  ('openpyxl/writer/excel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/excel.py',
   'DATA'),
  ('openpyxl/writer/theme.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/writer/theme.py',
   'DATA'),
  ('openpyxl/xml/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/__init__.py',
   'DATA'),
  ('openpyxl/xml/constants.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/constants.py',
   'DATA'),
  ('openpyxl/xml/functions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/openpyxl/xml/functions.py',
   'DATA'),
  ('pandas-2.3.0.dist-info/INSTALLER',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/INSTALLER',
   'DATA'),
  ('pandas-2.3.0.dist-info/LICENSE',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/LICENSE',
   'DATA'),
  ('pandas-2.3.0.dist-info/METADATA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/METADATA',
   'DATA'),
  ('pandas-2.3.0.dist-info/RECORD',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/RECORD',
   'DATA'),
  ('pandas-2.3.0.dist-info/REQUESTED',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/REQUESTED',
   'DATA'),
  ('pandas-2.3.0.dist-info/WHEEL',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/WHEEL',
   'DATA'),
  ('pandas-2.3.0.dist-info/entry_points.txt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas-2.3.0.dist-info/entry_points.txt',
   'DATA'),
  ('pandas/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/__init__.py',
   'DATA'),
  ('pandas/_config/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/__init__.py',
   'DATA'),
  ('pandas/_config/config.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/config.py',
   'DATA'),
  ('pandas/_config/dates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/dates.py',
   'DATA'),
  ('pandas/_config/display.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/display.py',
   'DATA'),
  ('pandas/_config/localization.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_config/localization.py',
   'DATA'),
  ('pandas/_libs/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/__init__.py',
   'DATA'),
  ('pandas/_libs/algos.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/algos.pyi',
   'DATA'),
  ('pandas/_libs/arrays.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/arrays.pyi',
   'DATA'),
  ('pandas/_libs/byteswap.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/byteswap.pyi',
   'DATA'),
  ('pandas/_libs/groupby.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/groupby.pyi',
   'DATA'),
  ('pandas/_libs/hashing.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashing.pyi',
   'DATA'),
  ('pandas/_libs/hashtable.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/hashtable.pyi',
   'DATA'),
  ('pandas/_libs/index.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/index.pyi',
   'DATA'),
  ('pandas/_libs/indexing.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/indexing.pyi',
   'DATA'),
  ('pandas/_libs/internals.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/internals.pyi',
   'DATA'),
  ('pandas/_libs/interval.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/interval.pyi',
   'DATA'),
  ('pandas/_libs/join.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/join.pyi',
   'DATA'),
  ('pandas/_libs/json.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/json.pyi',
   'DATA'),
  ('pandas/_libs/lib.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/lib.pyi',
   'DATA'),
  ('pandas/_libs/missing.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/missing.pyi',
   'DATA'),
  ('pandas/_libs/ops.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops.pyi',
   'DATA'),
  ('pandas/_libs/ops_dispatch.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/ops_dispatch.pyi',
   'DATA'),
  ('pandas/_libs/parsers.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/parsers.pyi',
   'DATA'),
  ('pandas/_libs/properties.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/properties.pyi',
   'DATA'),
  ('pandas/_libs/reshape.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/reshape.pyi',
   'DATA'),
  ('pandas/_libs/sas.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sas.pyi',
   'DATA'),
  ('pandas/_libs/sparse.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/sparse.pyi',
   'DATA'),
  ('pandas/_libs/testing.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/testing.pyi',
   'DATA'),
  ('pandas/_libs/tslib.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslib.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/__init__.py',
   'DATA'),
  ('pandas/_libs/tslibs/ccalendar.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/ccalendar.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/conversion.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/conversion.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/dtypes.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/dtypes.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/fields.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/fields.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/nattype.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/nattype.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/np_datetime.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/np_datetime.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/offsets.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/offsets.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/parsing.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/parsing.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/period.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/period.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/strptime.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/strptime.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/timedeltas.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timedeltas.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/timestamps.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timestamps.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/timezones.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/timezones.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/tzconversion.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/tzconversion.pyi',
   'DATA'),
  ('pandas/_libs/tslibs/vectorized.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/tslibs/vectorized.pyi',
   'DATA'),
  ('pandas/_libs/window/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/__init__.py',
   'DATA'),
  ('pandas/_libs/window/aggregations.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/aggregations.pyi',
   'DATA'),
  ('pandas/_libs/window/indexers.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/window/indexers.pyi',
   'DATA'),
  ('pandas/_libs/writers.pyi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_libs/writers.pyi',
   'DATA'),
  ('pandas/_testing/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/__init__.py',
   'DATA'),
  ('pandas/_testing/_hypothesis.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_hypothesis.py',
   'DATA'),
  ('pandas/_testing/_io.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_io.py',
   'DATA'),
  ('pandas/_testing/_warnings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/_warnings.py',
   'DATA'),
  ('pandas/_testing/asserters.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/asserters.py',
   'DATA'),
  ('pandas/_testing/compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/compat.py',
   'DATA'),
  ('pandas/_testing/contexts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_testing/contexts.py',
   'DATA'),
  ('pandas/_typing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_typing.py',
   'DATA'),
  ('pandas/_version.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version.py',
   'DATA'),
  ('pandas/_version_meson.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/_version_meson.py',
   'DATA'),
  ('pandas/api/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/__init__.py',
   'DATA'),
  ('pandas/api/extensions/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/extensions/__init__.py',
   'DATA'),
  ('pandas/api/indexers/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/indexers/__init__.py',
   'DATA'),
  ('pandas/api/interchange/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/interchange/__init__.py',
   'DATA'),
  ('pandas/api/types/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/types/__init__.py',
   'DATA'),
  ('pandas/api/typing/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/api/typing/__init__.py',
   'DATA'),
  ('pandas/arrays/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/arrays/__init__.py',
   'DATA'),
  ('pandas/compat/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/__init__.py',
   'DATA'),
  ('pandas/compat/_constants.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_constants.py',
   'DATA'),
  ('pandas/compat/_optional.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/_optional.py',
   'DATA'),
  ('pandas/compat/compressors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/compressors.py',
   'DATA'),
  ('pandas/compat/numpy/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/__init__.py',
   'DATA'),
  ('pandas/compat/numpy/function.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/numpy/function.py',
   'DATA'),
  ('pandas/compat/pickle_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pickle_compat.py',
   'DATA'),
  ('pandas/compat/pyarrow.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/compat/pyarrow.py',
   'DATA'),
  ('pandas/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/conftest.py',
   'DATA'),
  ('pandas/core/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/__init__.py',
   'DATA'),
  ('pandas/core/_numba/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/__init__.py',
   'DATA'),
  ('pandas/core/_numba/executor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/executor.py',
   'DATA'),
  ('pandas/core/_numba/extensions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/extensions.py',
   'DATA'),
  ('pandas/core/_numba/kernels/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/__init__.py',
   'DATA'),
  ('pandas/core/_numba/kernels/mean_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/mean_.py',
   'DATA'),
  ('pandas/core/_numba/kernels/min_max_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'DATA'),
  ('pandas/core/_numba/kernels/shared.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/shared.py',
   'DATA'),
  ('pandas/core/_numba/kernels/sum_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/sum_.py',
   'DATA'),
  ('pandas/core/_numba/kernels/var_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/_numba/kernels/var_.py',
   'DATA'),
  ('pandas/core/accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/accessor.py',
   'DATA'),
  ('pandas/core/algorithms.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/algorithms.py',
   'DATA'),
  ('pandas/core/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/api.py',
   'DATA'),
  ('pandas/core/apply.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py',
   'DATA'),
  ('pandas/core/array_algos/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/__init__.py',
   'DATA'),
  ('pandas/core/array_algos/datetimelike_accumulations.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'DATA'),
  ('pandas/core/array_algos/masked_accumulations.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'DATA'),
  ('pandas/core/array_algos/masked_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/masked_reductions.py',
   'DATA'),
  ('pandas/core/array_algos/putmask.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/putmask.py',
   'DATA'),
  ('pandas/core/array_algos/quantile.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/quantile.py',
   'DATA'),
  ('pandas/core/array_algos/replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/replace.py',
   'DATA'),
  ('pandas/core/array_algos/take.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/take.py',
   'DATA'),
  ('pandas/core/array_algos/transforms.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/array_algos/transforms.py',
   'DATA'),
  ('pandas/core/arraylike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arraylike.py',
   'DATA'),
  ('pandas/core/arrays/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/__init__.py',
   'DATA'),
  ('pandas/core/arrays/_arrow_string_mixins.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'DATA'),
  ('pandas/core/arrays/_mixins.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_mixins.py',
   'DATA'),
  ('pandas/core/arrays/_ranges.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_ranges.py',
   'DATA'),
  ('pandas/core/arrays/_utils.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/_utils.py',
   'DATA'),
  ('pandas/core/arrays/arrow/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/__init__.py',
   'DATA'),
  ('pandas/core/arrays/arrow/_arrow_utils.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'DATA'),
  ('pandas/core/arrays/arrow/accessors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/accessors.py',
   'DATA'),
  ('pandas/core/arrays/arrow/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/array.py',
   'DATA'),
  ('pandas/core/arrays/arrow/extension_types.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'DATA'),
  ('pandas/core/arrays/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/base.py',
   'DATA'),
  ('pandas/core/arrays/boolean.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/boolean.py',
   'DATA'),
  ('pandas/core/arrays/categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/categorical.py',
   'DATA'),
  ('pandas/core/arrays/datetimelike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimelike.py',
   'DATA'),
  ('pandas/core/arrays/datetimes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/datetimes.py',
   'DATA'),
  ('pandas/core/arrays/floating.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/floating.py',
   'DATA'),
  ('pandas/core/arrays/integer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/integer.py',
   'DATA'),
  ('pandas/core/arrays/interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/interval.py',
   'DATA'),
  ('pandas/core/arrays/masked.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/masked.py',
   'DATA'),
  ('pandas/core/arrays/numeric.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numeric.py',
   'DATA'),
  ('pandas/core/arrays/numpy_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/numpy_.py',
   'DATA'),
  ('pandas/core/arrays/period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/period.py',
   'DATA'),
  ('pandas/core/arrays/sparse/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/__init__.py',
   'DATA'),
  ('pandas/core/arrays/sparse/accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/accessor.py',
   'DATA'),
  ('pandas/core/arrays/sparse/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/array.py',
   'DATA'),
  ('pandas/core/arrays/sparse/scipy_sparse.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'DATA'),
  ('pandas/core/arrays/string_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_.py',
   'DATA'),
  ('pandas/core/arrays/string_arrow.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/string_arrow.py',
   'DATA'),
  ('pandas/core/arrays/timedeltas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/arrays/timedeltas.py',
   'DATA'),
  ('pandas/core/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/base.py',
   'DATA'),
  ('pandas/core/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/common.py',
   'DATA'),
  ('pandas/core/computation/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/__init__.py',
   'DATA'),
  ('pandas/core/computation/align.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/align.py',
   'DATA'),
  ('pandas/core/computation/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/api.py',
   'DATA'),
  ('pandas/core/computation/check.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/check.py',
   'DATA'),
  ('pandas/core/computation/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/common.py',
   'DATA'),
  ('pandas/core/computation/engines.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/engines.py',
   'DATA'),
  ('pandas/core/computation/eval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/eval.py',
   'DATA'),
  ('pandas/core/computation/expr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expr.py',
   'DATA'),
  ('pandas/core/computation/expressions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/expressions.py',
   'DATA'),
  ('pandas/core/computation/ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/ops.py',
   'DATA'),
  ('pandas/core/computation/parsing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/parsing.py',
   'DATA'),
  ('pandas/core/computation/pytables.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/pytables.py',
   'DATA'),
  ('pandas/core/computation/scope.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/computation/scope.py',
   'DATA'),
  ('pandas/core/config_init.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/config_init.py',
   'DATA'),
  ('pandas/core/construction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/construction.py',
   'DATA'),
  ('pandas/core/dtypes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/__init__.py',
   'DATA'),
  ('pandas/core/dtypes/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/api.py',
   'DATA'),
  ('pandas/core/dtypes/astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/astype.py',
   'DATA'),
  ('pandas/core/dtypes/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/base.py',
   'DATA'),
  ('pandas/core/dtypes/cast.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/cast.py',
   'DATA'),
  ('pandas/core/dtypes/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/common.py',
   'DATA'),
  ('pandas/core/dtypes/concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/concat.py',
   'DATA'),
  ('pandas/core/dtypes/dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/dtypes.py',
   'DATA'),
  ('pandas/core/dtypes/generic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/generic.py',
   'DATA'),
  ('pandas/core/dtypes/inference.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/inference.py',
   'DATA'),
  ('pandas/core/dtypes/missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/dtypes/missing.py',
   'DATA'),
  ('pandas/core/flags.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/flags.py',
   'DATA'),
  ('pandas/core/frame.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/frame.py',
   'DATA'),
  ('pandas/core/generic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/generic.py',
   'DATA'),
  ('pandas/core/groupby/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/__init__.py',
   'DATA'),
  ('pandas/core/groupby/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/base.py',
   'DATA'),
  ('pandas/core/groupby/categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/categorical.py',
   'DATA'),
  ('pandas/core/groupby/generic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/generic.py',
   'DATA'),
  ('pandas/core/groupby/groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/groupby.py',
   'DATA'),
  ('pandas/core/groupby/grouper.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/grouper.py',
   'DATA'),
  ('pandas/core/groupby/indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/indexing.py',
   'DATA'),
  ('pandas/core/groupby/numba_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/numba_.py',
   'DATA'),
  ('pandas/core/groupby/ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/groupby/ops.py',
   'DATA'),
  ('pandas/core/indexers/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/__init__.py',
   'DATA'),
  ('pandas/core/indexers/objects.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/objects.py',
   'DATA'),
  ('pandas/core/indexers/utils.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexers/utils.py',
   'DATA'),
  ('pandas/core/indexes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/__init__.py',
   'DATA'),
  ('pandas/core/indexes/accessors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/accessors.py',
   'DATA'),
  ('pandas/core/indexes/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/api.py',
   'DATA'),
  ('pandas/core/indexes/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/base.py',
   'DATA'),
  ('pandas/core/indexes/category.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/category.py',
   'DATA'),
  ('pandas/core/indexes/datetimelike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimelike.py',
   'DATA'),
  ('pandas/core/indexes/datetimes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/datetimes.py',
   'DATA'),
  ('pandas/core/indexes/extension.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/extension.py',
   'DATA'),
  ('pandas/core/indexes/frozen.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/frozen.py',
   'DATA'),
  ('pandas/core/indexes/interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/interval.py',
   'DATA'),
  ('pandas/core/indexes/multi.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/multi.py',
   'DATA'),
  ('pandas/core/indexes/period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/period.py',
   'DATA'),
  ('pandas/core/indexes/range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/range.py',
   'DATA'),
  ('pandas/core/indexes/timedeltas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexes/timedeltas.py',
   'DATA'),
  ('pandas/core/indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/indexing.py',
   'DATA'),
  ('pandas/core/interchange/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/__init__.py',
   'DATA'),
  ('pandas/core/interchange/buffer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/buffer.py',
   'DATA'),
  ('pandas/core/interchange/column.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/column.py',
   'DATA'),
  ('pandas/core/interchange/dataframe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe.py',
   'DATA'),
  ('pandas/core/interchange/dataframe_protocol.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'DATA'),
  ('pandas/core/interchange/from_dataframe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/from_dataframe.py',
   'DATA'),
  ('pandas/core/interchange/utils.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/interchange/utils.py',
   'DATA'),
  ('pandas/core/internals/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/__init__.py',
   'DATA'),
  ('pandas/core/internals/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/api.py',
   'DATA'),
  ('pandas/core/internals/array_manager.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/array_manager.py',
   'DATA'),
  ('pandas/core/internals/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/base.py',
   'DATA'),
  ('pandas/core/internals/blocks.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/blocks.py',
   'DATA'),
  ('pandas/core/internals/concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/concat.py',
   'DATA'),
  ('pandas/core/internals/construction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/construction.py',
   'DATA'),
  ('pandas/core/internals/managers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/managers.py',
   'DATA'),
  ('pandas/core/internals/ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/internals/ops.py',
   'DATA'),
  ('pandas/core/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/__init__.py',
   'DATA'),
  ('pandas/core/methods/describe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/describe.py',
   'DATA'),
  ('pandas/core/methods/selectn.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/selectn.py',
   'DATA'),
  ('pandas/core/methods/to_dict.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/methods/to_dict.py',
   'DATA'),
  ('pandas/core/missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/missing.py',
   'DATA'),
  ('pandas/core/nanops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/nanops.py',
   'DATA'),
  ('pandas/core/ops/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/__init__.py',
   'DATA'),
  ('pandas/core/ops/array_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/array_ops.py',
   'DATA'),
  ('pandas/core/ops/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/common.py',
   'DATA'),
  ('pandas/core/ops/dispatch.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/dispatch.py',
   'DATA'),
  ('pandas/core/ops/docstrings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/docstrings.py',
   'DATA'),
  ('pandas/core/ops/invalid.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/invalid.py',
   'DATA'),
  ('pandas/core/ops/mask_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/mask_ops.py',
   'DATA'),
  ('pandas/core/ops/missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/ops/missing.py',
   'DATA'),
  ('pandas/core/resample.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/resample.py',
   'DATA'),
  ('pandas/core/reshape/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/__init__.py',
   'DATA'),
  ('pandas/core/reshape/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/api.py',
   'DATA'),
  ('pandas/core/reshape/concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/concat.py',
   'DATA'),
  ('pandas/core/reshape/encoding.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/encoding.py',
   'DATA'),
  ('pandas/core/reshape/melt.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/melt.py',
   'DATA'),
  ('pandas/core/reshape/merge.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/merge.py',
   'DATA'),
  ('pandas/core/reshape/pivot.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/pivot.py',
   'DATA'),
  ('pandas/core/reshape/reshape.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/reshape.py',
   'DATA'),
  ('pandas/core/reshape/tile.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/tile.py',
   'DATA'),
  ('pandas/core/reshape/util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/reshape/util.py',
   'DATA'),
  ('pandas/core/roperator.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/roperator.py',
   'DATA'),
  ('pandas/core/sample.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sample.py',
   'DATA'),
  ('pandas/core/series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/series.py',
   'DATA'),
  ('pandas/core/shared_docs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/shared_docs.py',
   'DATA'),
  ('pandas/core/sorting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sorting.py',
   'DATA'),
  ('pandas/core/sparse/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sparse/__init__.py',
   'DATA'),
  ('pandas/core/sparse/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/sparse/api.py',
   'DATA'),
  ('pandas/core/strings/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/__init__.py',
   'DATA'),
  ('pandas/core/strings/accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py',
   'DATA'),
  ('pandas/core/strings/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/base.py',
   'DATA'),
  ('pandas/core/strings/object_array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/object_array.py',
   'DATA'),
  ('pandas/core/tools/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/__init__.py',
   'DATA'),
  ('pandas/core/tools/datetimes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/datetimes.py',
   'DATA'),
  ('pandas/core/tools/numeric.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/numeric.py',
   'DATA'),
  ('pandas/core/tools/timedeltas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/timedeltas.py',
   'DATA'),
  ('pandas/core/tools/times.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/tools/times.py',
   'DATA'),
  ('pandas/core/util/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/__init__.py',
   'DATA'),
  ('pandas/core/util/hashing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/hashing.py',
   'DATA'),
  ('pandas/core/util/numba_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/util/numba_.py',
   'DATA'),
  ('pandas/core/window/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/__init__.py',
   'DATA'),
  ('pandas/core/window/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/common.py',
   'DATA'),
  ('pandas/core/window/doc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/doc.py',
   'DATA'),
  ('pandas/core/window/ewm.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/ewm.py',
   'DATA'),
  ('pandas/core/window/expanding.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/expanding.py',
   'DATA'),
  ('pandas/core/window/numba_.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/numba_.py',
   'DATA'),
  ('pandas/core/window/online.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/online.py',
   'DATA'),
  ('pandas/core/window/rolling.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/window/rolling.py',
   'DATA'),
  ('pandas/errors/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/errors/__init__.py',
   'DATA'),
  ('pandas/io/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/__init__.py',
   'DATA'),
  ('pandas/io/_util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/_util.py',
   'DATA'),
  ('pandas/io/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/api.py',
   'DATA'),
  ('pandas/io/clipboard/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboard/__init__.py',
   'DATA'),
  ('pandas/io/clipboards.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/clipboards.py',
   'DATA'),
  ('pandas/io/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/common.py',
   'DATA'),
  ('pandas/io/excel/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/__init__.py',
   'DATA'),
  ('pandas/io/excel/_base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_base.py',
   'DATA'),
  ('pandas/io/excel/_calamine.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_calamine.py',
   'DATA'),
  ('pandas/io/excel/_odfreader.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odfreader.py',
   'DATA'),
  ('pandas/io/excel/_odswriter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_odswriter.py',
   'DATA'),
  ('pandas/io/excel/_openpyxl.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_openpyxl.py',
   'DATA'),
  ('pandas/io/excel/_pyxlsb.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_pyxlsb.py',
   'DATA'),
  ('pandas/io/excel/_util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_util.py',
   'DATA'),
  ('pandas/io/excel/_xlrd.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlrd.py',
   'DATA'),
  ('pandas/io/excel/_xlsxwriter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/excel/_xlsxwriter.py',
   'DATA'),
  ('pandas/io/feather_format.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/feather_format.py',
   'DATA'),
  ('pandas/io/formats/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/__init__.py',
   'DATA'),
  ('pandas/io/formats/_color_data.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/_color_data.py',
   'DATA'),
  ('pandas/io/formats/console.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/console.py',
   'DATA'),
  ('pandas/io/formats/css.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/css.py',
   'DATA'),
  ('pandas/io/formats/csvs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/csvs.py',
   'DATA'),
  ('pandas/io/formats/excel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/excel.py',
   'DATA'),
  ('pandas/io/formats/format.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/format.py',
   'DATA'),
  ('pandas/io/formats/html.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/html.py',
   'DATA'),
  ('pandas/io/formats/info.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/info.py',
   'DATA'),
  ('pandas/io/formats/printing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/printing.py',
   'DATA'),
  ('pandas/io/formats/string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/string.py',
   'DATA'),
  ('pandas/io/formats/style.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style.py',
   'DATA'),
  ('pandas/io/formats/style_render.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/style_render.py',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pandas/io/formats/xml.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/formats/xml.py',
   'DATA'),
  ('pandas/io/gbq.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/gbq.py',
   'DATA'),
  ('pandas/io/html.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/html.py',
   'DATA'),
  ('pandas/io/json/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/__init__.py',
   'DATA'),
  ('pandas/io/json/_json.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_json.py',
   'DATA'),
  ('pandas/io/json/_normalize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_normalize.py',
   'DATA'),
  ('pandas/io/json/_table_schema.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/json/_table_schema.py',
   'DATA'),
  ('pandas/io/orc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/orc.py',
   'DATA'),
  ('pandas/io/parquet.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parquet.py',
   'DATA'),
  ('pandas/io/parsers/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/__init__.py',
   'DATA'),
  ('pandas/io/parsers/arrow_parser_wrapper.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'DATA'),
  ('pandas/io/parsers/base_parser.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/base_parser.py',
   'DATA'),
  ('pandas/io/parsers/c_parser_wrapper.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'DATA'),
  ('pandas/io/parsers/python_parser.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/python_parser.py',
   'DATA'),
  ('pandas/io/parsers/readers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py',
   'DATA'),
  ('pandas/io/pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pickle.py',
   'DATA'),
  ('pandas/io/pytables.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/pytables.py',
   'DATA'),
  ('pandas/io/sas/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/__init__.py',
   'DATA'),
  ('pandas/io/sas/sas7bdat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas7bdat.py',
   'DATA'),
  ('pandas/io/sas/sas_constants.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_constants.py',
   'DATA'),
  ('pandas/io/sas/sas_xport.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sas_xport.py',
   'DATA'),
  ('pandas/io/sas/sasreader.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sas/sasreader.py',
   'DATA'),
  ('pandas/io/spss.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/spss.py',
   'DATA'),
  ('pandas/io/sql.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/sql.py',
   'DATA'),
  ('pandas/io/stata.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/stata.py',
   'DATA'),
  ('pandas/io/xml.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/io/xml.py',
   'DATA'),
  ('pandas/plotting/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/__init__.py',
   'DATA'),
  ('pandas/plotting/_core.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_core.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/__init__.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/boxplot.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/boxplot.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/converter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/converter.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/core.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/core.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/groupby.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/hist.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/hist.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/misc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/misc.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/style.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/style.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/timeseries.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/timeseries.py',
   'DATA'),
  ('pandas/plotting/_matplotlib/tools.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_matplotlib/tools.py',
   'DATA'),
  ('pandas/plotting/_misc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/plotting/_misc.py',
   'DATA'),
  ('pandas/pyproject.toml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/pyproject.toml',
   'DATA'),
  ('pandas/testing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/testing.py',
   'DATA'),
  ('pandas/tests/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/__init__.py',
   'DATA'),
  ('pandas/tests/api/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/api/__init__.py',
   'DATA'),
  ('pandas/tests/api/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/api/test_api.py',
   'DATA'),
  ('pandas/tests/api/test_types.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/api/test_types.py',
   'DATA'),
  ('pandas/tests/apply/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/__init__.py',
   'DATA'),
  ('pandas/tests/apply/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/common.py',
   'DATA'),
  ('pandas/tests/apply/test_frame_apply.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_frame_apply.py',
   'DATA'),
  ('pandas/tests/apply/test_frame_apply_relabeling.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_frame_apply_relabeling.py',
   'DATA'),
  ('pandas/tests/apply/test_frame_transform.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_frame_transform.py',
   'DATA'),
  ('pandas/tests/apply/test_invalid_arg.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_invalid_arg.py',
   'DATA'),
  ('pandas/tests/apply/test_numba.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_numba.py',
   'DATA'),
  ('pandas/tests/apply/test_series_apply.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_series_apply.py',
   'DATA'),
  ('pandas/tests/apply/test_series_apply_relabeling.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_series_apply_relabeling.py',
   'DATA'),
  ('pandas/tests/apply/test_series_transform.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_series_transform.py',
   'DATA'),
  ('pandas/tests/apply/test_str.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/apply/test_str.py',
   'DATA'),
  ('pandas/tests/arithmetic/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/__init__.py',
   'DATA'),
  ('pandas/tests/arithmetic/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/common.py',
   'DATA'),
  ('pandas/tests/arithmetic/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/conftest.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_array_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_array_ops.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_categorical.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_datetime64.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_datetime64.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_interval.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_numeric.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_numeric.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_object.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_object.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_period.py',
   'DATA'),
  ('pandas/tests/arithmetic/test_timedelta64.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arithmetic/test_timedelta64.py',
   'DATA'),
  ('pandas/tests/arrays/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_astype.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_comparison.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_comparison.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_construction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_construction.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_function.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_function.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_indexing.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_logical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_logical.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_ops.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_reduction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_reduction.py',
   'DATA'),
  ('pandas/tests/arrays/boolean/test_repr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/boolean/test_repr.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_algos.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_algos.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_analytics.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_analytics.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_api.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_astype.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_constructors.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_dtypes.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_indexing.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_map.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_map.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_missing.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_operators.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_operators.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_replace.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_repr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_repr.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_sorting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_sorting.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_subclass.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_subclass.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_take.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_take.py',
   'DATA'),
  ('pandas/tests/arrays/categorical/test_warnings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/categorical/test_warnings.py',
   'DATA'),
  ('pandas/tests/arrays/datetimes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/datetimes/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/test_constructors.py',
   'DATA'),
  ('pandas/tests/arrays/datetimes/test_cumulative.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/test_cumulative.py',
   'DATA'),
  ('pandas/tests/arrays/datetimes/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/datetimes/test_reductions.py',
   'DATA'),
  ('pandas/tests/arrays/floating/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/floating/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/conftest.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_astype.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_comparison.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_comparison.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_concat.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_construction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_construction.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_contains.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_contains.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_function.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_function.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_repr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_repr.py',
   'DATA'),
  ('pandas/tests/arrays/floating/test_to_numpy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/floating/test_to_numpy.py',
   'DATA'),
  ('pandas/tests/arrays/integer/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/integer/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/conftest.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_comparison.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_comparison.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_concat.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_construction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_construction.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_dtypes.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_function.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_function.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_indexing.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_reduction.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_reduction.py',
   'DATA'),
  ('pandas/tests/arrays/integer/test_repr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/integer/test_repr.py',
   'DATA'),
  ('pandas/tests/arrays/interval/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/interval/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_astype.py',
   'DATA'),
  ('pandas/tests/arrays/interval/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_formats.py',
   'DATA'),
  ('pandas/tests/arrays/interval/test_interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_interval.py',
   'DATA'),
  ('pandas/tests/arrays/interval/test_interval_pyarrow.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_interval_pyarrow.py',
   'DATA'),
  ('pandas/tests/arrays/interval/test_overlaps.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/interval/test_overlaps.py',
   'DATA'),
  ('pandas/tests/arrays/masked/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/masked/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/arrays/masked/test_arrow_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_arrow_compat.py',
   'DATA'),
  ('pandas/tests/arrays/masked/test_function.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_function.py',
   'DATA'),
  ('pandas/tests/arrays/masked/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked/test_indexing.py',
   'DATA'),
  ('pandas/tests/arrays/masked_shared.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/masked_shared.py',
   'DATA'),
  ('pandas/tests/arrays/numpy_/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/numpy_/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/numpy_/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/numpy_/test_indexing.py',
   'DATA'),
  ('pandas/tests/arrays/numpy_/test_numpy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/numpy_/test_numpy.py',
   'DATA'),
  ('pandas/tests/arrays/period/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/period/test_arrow_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_arrow_compat.py',
   'DATA'),
  ('pandas/tests/arrays/period/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_astype.py',
   'DATA'),
  ('pandas/tests/arrays/period/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_constructors.py',
   'DATA'),
  ('pandas/tests/arrays/period/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/period/test_reductions.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_accessor.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_arithmetics.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_arithmetics.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_array.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_astype.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_combine_concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_combine_concat.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_constructors.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_dtype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_dtype.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_indexing.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_libsparse.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_libsparse.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_reductions.py',
   'DATA'),
  ('pandas/tests/arrays/sparse/test_unary.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/sparse/test_unary.py',
   'DATA'),
  ('pandas/tests/arrays/string_/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/string_/test_concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/test_concat.py',
   'DATA'),
  ('pandas/tests/arrays/string_/test_string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/test_string.py',
   'DATA'),
  ('pandas/tests/arrays/string_/test_string_arrow.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/string_/test_string_arrow.py',
   'DATA'),
  ('pandas/tests/arrays/test_array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_array.py',
   'DATA'),
  ('pandas/tests/arrays/test_datetimelike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_datetimelike.py',
   'DATA'),
  ('pandas/tests/arrays/test_datetimes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_datetimes.py',
   'DATA'),
  ('pandas/tests/arrays/test_ndarray_backed.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_ndarray_backed.py',
   'DATA'),
  ('pandas/tests/arrays/test_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_period.py',
   'DATA'),
  ('pandas/tests/arrays/test_timedeltas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/test_timedeltas.py',
   'DATA'),
  ('pandas/tests/arrays/timedeltas/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/__init__.py',
   'DATA'),
  ('pandas/tests/arrays/timedeltas/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/test_constructors.py',
   'DATA'),
  ('pandas/tests/arrays/timedeltas/test_cumulative.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/test_cumulative.py',
   'DATA'),
  ('pandas/tests/arrays/timedeltas/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/arrays/timedeltas/test_reductions.py',
   'DATA'),
  ('pandas/tests/base/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/__init__.py',
   'DATA'),
  ('pandas/tests/base/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/common.py',
   'DATA'),
  ('pandas/tests/base/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_constructors.py',
   'DATA'),
  ('pandas/tests/base/test_conversion.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_conversion.py',
   'DATA'),
  ('pandas/tests/base/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_fillna.py',
   'DATA'),
  ('pandas/tests/base/test_misc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_misc.py',
   'DATA'),
  ('pandas/tests/base/test_transpose.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_transpose.py',
   'DATA'),
  ('pandas/tests/base/test_unique.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_unique.py',
   'DATA'),
  ('pandas/tests/base/test_value_counts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/base/test_value_counts.py',
   'DATA'),
  ('pandas/tests/computation/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/computation/__init__.py',
   'DATA'),
  ('pandas/tests/computation/test_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/computation/test_compat.py',
   'DATA'),
  ('pandas/tests/computation/test_eval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/computation/test_eval.py',
   'DATA'),
  ('pandas/tests/config/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/config/__init__.py',
   'DATA'),
  ('pandas/tests/config/test_config.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/config/test_config.py',
   'DATA'),
  ('pandas/tests/config/test_localization.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/config/test_localization.py',
   'DATA'),
  ('pandas/tests/construction/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/construction/__init__.py',
   'DATA'),
  ('pandas/tests/construction/test_extract_array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/construction/test_extract_array.py',
   'DATA'),
  ('pandas/tests/copy_view/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/__init__.py',
   'DATA'),
  ('pandas/tests/copy_view/index/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/__init__.py',
   'DATA'),
  ('pandas/tests/copy_view/index/test_datetimeindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_datetimeindex.py',
   'DATA'),
  ('pandas/tests/copy_view/index/test_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_index.py',
   'DATA'),
  ('pandas/tests/copy_view/index/test_periodindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_periodindex.py',
   'DATA'),
  ('pandas/tests/copy_view/index/test_timedeltaindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/index/test_timedeltaindex.py',
   'DATA'),
  ('pandas/tests/copy_view/test_array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_array.py',
   'DATA'),
  ('pandas/tests/copy_view/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_astype.py',
   'DATA'),
  ('pandas/tests/copy_view/test_chained_assignment_deprecation.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_chained_assignment_deprecation.py',
   'DATA'),
  ('pandas/tests/copy_view/test_clip.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_clip.py',
   'DATA'),
  ('pandas/tests/copy_view/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_constructors.py',
   'DATA'),
  ('pandas/tests/copy_view/test_core_functionalities.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_core_functionalities.py',
   'DATA'),
  ('pandas/tests/copy_view/test_functions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_functions.py',
   'DATA'),
  ('pandas/tests/copy_view/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_indexing.py',
   'DATA'),
  ('pandas/tests/copy_view/test_internals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_internals.py',
   'DATA'),
  ('pandas/tests/copy_view/test_interp_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_interp_fillna.py',
   'DATA'),
  ('pandas/tests/copy_view/test_methods.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_methods.py',
   'DATA'),
  ('pandas/tests/copy_view/test_replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_replace.py',
   'DATA'),
  ('pandas/tests/copy_view/test_setitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_setitem.py',
   'DATA'),
  ('pandas/tests/copy_view/test_util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/test_util.py',
   'DATA'),
  ('pandas/tests/copy_view/util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/copy_view/util.py',
   'DATA'),
  ('pandas/tests/dtypes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/__init__.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/__init__.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_can_hold_element.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_can_hold_element.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_construct_from_scalar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_construct_from_scalar.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_construct_ndarray.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_construct_ndarray.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_construct_object_arr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_construct_object_arr.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_dict_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_dict_compat.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_downcast.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_downcast.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_find_common_type.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_find_common_type.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_infer_datetimelike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_infer_datetimelike.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_infer_dtype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_infer_dtype.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_maybe_box_native.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_maybe_box_native.py',
   'DATA'),
  ('pandas/tests/dtypes/cast/test_promote.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/cast/test_promote.py',
   'DATA'),
  ('pandas/tests/dtypes/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_common.py',
   'DATA'),
  ('pandas/tests/dtypes/test_concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_concat.py',
   'DATA'),
  ('pandas/tests/dtypes/test_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_dtypes.py',
   'DATA'),
  ('pandas/tests/dtypes/test_generic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_generic.py',
   'DATA'),
  ('pandas/tests/dtypes/test_inference.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_inference.py',
   'DATA'),
  ('pandas/tests/dtypes/test_missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/dtypes/test_missing.py',
   'DATA'),
  ('pandas/tests/extension/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/__init__.py',
   'DATA'),
  ('pandas/tests/extension/array_with_attr/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/array_with_attr/__init__.py',
   'DATA'),
  ('pandas/tests/extension/array_with_attr/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/array_with_attr/array.py',
   'DATA'),
  ('pandas/tests/extension/array_with_attr/test_array_with_attr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/array_with_attr/test_array_with_attr.py',
   'DATA'),
  ('pandas/tests/extension/base/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/__init__.py',
   'DATA'),
  ('pandas/tests/extension/base/accumulate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/accumulate.py',
   'DATA'),
  ('pandas/tests/extension/base/base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/base.py',
   'DATA'),
  ('pandas/tests/extension/base/casting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/casting.py',
   'DATA'),
  ('pandas/tests/extension/base/constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/constructors.py',
   'DATA'),
  ('pandas/tests/extension/base/dim2.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/dim2.py',
   'DATA'),
  ('pandas/tests/extension/base/dtype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/dtype.py',
   'DATA'),
  ('pandas/tests/extension/base/getitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/getitem.py',
   'DATA'),
  ('pandas/tests/extension/base/groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/groupby.py',
   'DATA'),
  ('pandas/tests/extension/base/index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/index.py',
   'DATA'),
  ('pandas/tests/extension/base/interface.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/interface.py',
   'DATA'),
  ('pandas/tests/extension/base/io.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/io.py',
   'DATA'),
  ('pandas/tests/extension/base/methods.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/methods.py',
   'DATA'),
  ('pandas/tests/extension/base/missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/missing.py',
   'DATA'),
  ('pandas/tests/extension/base/ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/ops.py',
   'DATA'),
  ('pandas/tests/extension/base/printing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/printing.py',
   'DATA'),
  ('pandas/tests/extension/base/reduce.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/reduce.py',
   'DATA'),
  ('pandas/tests/extension/base/reshaping.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/reshaping.py',
   'DATA'),
  ('pandas/tests/extension/base/setitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/base/setitem.py',
   'DATA'),
  ('pandas/tests/extension/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/conftest.py',
   'DATA'),
  ('pandas/tests/extension/date/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/date/__init__.py',
   'DATA'),
  ('pandas/tests/extension/date/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/date/array.py',
   'DATA'),
  ('pandas/tests/extension/decimal/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/decimal/__init__.py',
   'DATA'),
  ('pandas/tests/extension/decimal/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/decimal/array.py',
   'DATA'),
  ('pandas/tests/extension/decimal/test_decimal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/decimal/test_decimal.py',
   'DATA'),
  ('pandas/tests/extension/json/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/json/__init__.py',
   'DATA'),
  ('pandas/tests/extension/json/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/json/array.py',
   'DATA'),
  ('pandas/tests/extension/json/test_json.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/json/test_json.py',
   'DATA'),
  ('pandas/tests/extension/list/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/list/__init__.py',
   'DATA'),
  ('pandas/tests/extension/list/array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/list/array.py',
   'DATA'),
  ('pandas/tests/extension/list/test_list.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/list/test_list.py',
   'DATA'),
  ('pandas/tests/extension/test_arrow.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_arrow.py',
   'DATA'),
  ('pandas/tests/extension/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_categorical.py',
   'DATA'),
  ('pandas/tests/extension/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_common.py',
   'DATA'),
  ('pandas/tests/extension/test_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_datetime.py',
   'DATA'),
  ('pandas/tests/extension/test_extension.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_extension.py',
   'DATA'),
  ('pandas/tests/extension/test_interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_interval.py',
   'DATA'),
  ('pandas/tests/extension/test_masked.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_masked.py',
   'DATA'),
  ('pandas/tests/extension/test_numpy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_numpy.py',
   'DATA'),
  ('pandas/tests/extension/test_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_period.py',
   'DATA'),
  ('pandas/tests/extension/test_sparse.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_sparse.py',
   'DATA'),
  ('pandas/tests/extension/test_string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/extension/test_string.py',
   'DATA'),
  ('pandas/tests/frame/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/__init__.py',
   'DATA'),
  ('pandas/tests/frame/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/common.py',
   'DATA'),
  ('pandas/tests/frame/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/conftest.py',
   'DATA'),
  ('pandas/tests/frame/constructors/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/constructors/__init__.py',
   'DATA'),
  ('pandas/tests/frame/constructors/test_from_dict.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/constructors/test_from_dict.py',
   'DATA'),
  ('pandas/tests/frame/constructors/test_from_records.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/constructors/test_from_records.py',
   'DATA'),
  ('pandas/tests/frame/indexing/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/__init__.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_coercion.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_coercion.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_delitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_delitem.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_get.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_get.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_get_value.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_get_value.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_getitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_getitem.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_indexing.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_insert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_insert.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_mask.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_mask.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_set_value.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_set_value.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_setitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_setitem.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_take.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_take.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_where.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_where.py',
   'DATA'),
  ('pandas/tests/frame/indexing/test_xs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/indexing/test_xs.py',
   'DATA'),
  ('pandas/tests/frame/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/__init__.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_add_prefix_suffix.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_add_prefix_suffix.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_align.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_align.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_asfreq.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_asfreq.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_asof.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_asof.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_assign.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_assign.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_astype.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_at_time.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_at_time.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_between_time.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_between_time.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_clip.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_clip.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_combine.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_combine.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_combine_first.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_combine_first.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_compare.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_compare.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_convert_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_convert_dtypes.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_copy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_copy.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_count.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_count.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_cov_corr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_cov_corr.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_describe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_describe.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_diff.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_diff.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_dot.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_dot.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_drop.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_drop.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_drop_duplicates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_drop_duplicates.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_droplevel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_droplevel.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_dropna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_dropna.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_dtypes.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_duplicated.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_duplicated.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_equals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_equals.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_explode.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_explode.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_fillna.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_filter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_filter.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_first_and_last.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_first_and_last.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_first_valid_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_first_valid_index.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_get_numeric_data.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_get_numeric_data.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_head_tail.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_head_tail.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_infer_objects.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_infer_objects.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_info.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_info.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_interpolate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_interpolate.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_is_homogeneous_dtype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_is_homogeneous_dtype.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_isetitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_isetitem.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_isin.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_isin.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_iterrows.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_iterrows.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_join.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_map.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_map.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_matmul.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_matmul.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_nlargest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_nlargest.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_pct_change.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_pct_change.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_pipe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_pipe.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_pop.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_pop.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_quantile.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_quantile.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_rank.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_rank.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_reindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reindex.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_reindex_like.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reindex_like.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_rename.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_rename.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_rename_axis.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_rename_axis.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_reorder_levels.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reorder_levels.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_replace.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_reset_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_reset_index.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_round.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_round.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_sample.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_sample.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_select_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_select_dtypes.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_set_axis.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_set_axis.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_set_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_set_index.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_shift.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_shift.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_size.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_size.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_sort_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_sort_index.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_sort_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_sort_values.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_swapaxes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_swapaxes.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_swaplevel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_swaplevel.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_csv.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_csv.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_dict.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_dict.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_dict_of_blocks.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_dict_of_blocks.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_numpy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_numpy.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_period.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_records.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_records.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_to_timestamp.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_to_timestamp.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_transpose.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_transpose.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_truncate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_truncate.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_tz_convert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_tz_convert.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_tz_localize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_tz_localize.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_update.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_update.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_value_counts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_value_counts.py',
   'DATA'),
  ('pandas/tests/frame/methods/test_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/methods/test_values.py',
   'DATA'),
  ('pandas/tests/frame/test_alter_axes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_alter_axes.py',
   'DATA'),
  ('pandas/tests/frame/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_api.py',
   'DATA'),
  ('pandas/tests/frame/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/frame/test_arrow_interface.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_arrow_interface.py',
   'DATA'),
  ('pandas/tests/frame/test_block_internals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_block_internals.py',
   'DATA'),
  ('pandas/tests/frame/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_constructors.py',
   'DATA'),
  ('pandas/tests/frame/test_cumulative.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_cumulative.py',
   'DATA'),
  ('pandas/tests/frame/test_iteration.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_iteration.py',
   'DATA'),
  ('pandas/tests/frame/test_logical_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_logical_ops.py',
   'DATA'),
  ('pandas/tests/frame/test_nonunique_indexes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_nonunique_indexes.py',
   'DATA'),
  ('pandas/tests/frame/test_npfuncs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_npfuncs.py',
   'DATA'),
  ('pandas/tests/frame/test_query_eval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_query_eval.py',
   'DATA'),
  ('pandas/tests/frame/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_reductions.py',
   'DATA'),
  ('pandas/tests/frame/test_repr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_repr.py',
   'DATA'),
  ('pandas/tests/frame/test_stack_unstack.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_stack_unstack.py',
   'DATA'),
  ('pandas/tests/frame/test_subclass.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_subclass.py',
   'DATA'),
  ('pandas/tests/frame/test_ufunc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_ufunc.py',
   'DATA'),
  ('pandas/tests/frame/test_unary.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_unary.py',
   'DATA'),
  ('pandas/tests/frame/test_validate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/frame/test_validate.py',
   'DATA'),
  ('pandas/tests/generic/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/__init__.py',
   'DATA'),
  ('pandas/tests/generic/test_duplicate_labels.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_duplicate_labels.py',
   'DATA'),
  ('pandas/tests/generic/test_finalize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_finalize.py',
   'DATA'),
  ('pandas/tests/generic/test_frame.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_frame.py',
   'DATA'),
  ('pandas/tests/generic/test_generic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_generic.py',
   'DATA'),
  ('pandas/tests/generic/test_label_or_level_utils.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_label_or_level_utils.py',
   'DATA'),
  ('pandas/tests/generic/test_series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_series.py',
   'DATA'),
  ('pandas/tests/generic/test_to_xarray.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/generic/test_to_xarray.py',
   'DATA'),
  ('pandas/tests/groupby/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/__init__.py',
   'DATA'),
  ('pandas/tests/groupby/aggregate/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/__init__.py',
   'DATA'),
  ('pandas/tests/groupby/aggregate/test_aggregate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_aggregate.py',
   'DATA'),
  ('pandas/tests/groupby/aggregate/test_cython.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_cython.py',
   'DATA'),
  ('pandas/tests/groupby/aggregate/test_numba.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_numba.py',
   'DATA'),
  ('pandas/tests/groupby/aggregate/test_other.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/aggregate/test_other.py',
   'DATA'),
  ('pandas/tests/groupby/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/conftest.py',
   'DATA'),
  ('pandas/tests/groupby/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/__init__.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_corrwith.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_corrwith.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_describe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_describe.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_groupby_shift_diff.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_groupby_shift_diff.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_is_monotonic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_is_monotonic.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_nlargest_nsmallest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_nlargest_nsmallest.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_nth.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_nth.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_quantile.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_quantile.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_rank.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_rank.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_sample.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_sample.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_size.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_size.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_skew.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_skew.py',
   'DATA'),
  ('pandas/tests/groupby/methods/test_value_counts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/methods/test_value_counts.py',
   'DATA'),
  ('pandas/tests/groupby/test_all_methods.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_all_methods.py',
   'DATA'),
  ('pandas/tests/groupby/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_api.py',
   'DATA'),
  ('pandas/tests/groupby/test_apply.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_apply.py',
   'DATA'),
  ('pandas/tests/groupby/test_apply_mutate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_apply_mutate.py',
   'DATA'),
  ('pandas/tests/groupby/test_bin_groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_bin_groupby.py',
   'DATA'),
  ('pandas/tests/groupby/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_categorical.py',
   'DATA'),
  ('pandas/tests/groupby/test_counting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_counting.py',
   'DATA'),
  ('pandas/tests/groupby/test_cumulative.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_cumulative.py',
   'DATA'),
  ('pandas/tests/groupby/test_filters.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_filters.py',
   'DATA'),
  ('pandas/tests/groupby/test_groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_groupby.py',
   'DATA'),
  ('pandas/tests/groupby/test_groupby_dropna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_groupby_dropna.py',
   'DATA'),
  ('pandas/tests/groupby/test_groupby_subclass.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_groupby_subclass.py',
   'DATA'),
  ('pandas/tests/groupby/test_grouping.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_grouping.py',
   'DATA'),
  ('pandas/tests/groupby/test_index_as_string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_index_as_string.py',
   'DATA'),
  ('pandas/tests/groupby/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_indexing.py',
   'DATA'),
  ('pandas/tests/groupby/test_libgroupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_libgroupby.py',
   'DATA'),
  ('pandas/tests/groupby/test_missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_missing.py',
   'DATA'),
  ('pandas/tests/groupby/test_numba.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_numba.py',
   'DATA'),
  ('pandas/tests/groupby/test_numeric_only.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_numeric_only.py',
   'DATA'),
  ('pandas/tests/groupby/test_pipe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_pipe.py',
   'DATA'),
  ('pandas/tests/groupby/test_raises.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_raises.py',
   'DATA'),
  ('pandas/tests/groupby/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_reductions.py',
   'DATA'),
  ('pandas/tests/groupby/test_timegrouper.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/test_timegrouper.py',
   'DATA'),
  ('pandas/tests/groupby/transform/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/transform/__init__.py',
   'DATA'),
  ('pandas/tests/groupby/transform/test_numba.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/transform/test_numba.py',
   'DATA'),
  ('pandas/tests/groupby/transform/test_transform.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/groupby/transform/test_transform.py',
   'DATA'),
  ('pandas/tests/indexes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_pickle.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_reshape.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_reshape.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/base_class/test_where.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/base_class/test_where.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_append.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_append.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_category.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_category.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_equals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_equals.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_fillna.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_map.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_map.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_reindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_reindex.py',
   'DATA'),
  ('pandas/tests/indexes/categorical/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/categorical/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/conftest.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_drop_duplicates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_drop_duplicates.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_equals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_equals.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_is_monotonic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_is_monotonic.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_nat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_nat.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_sort_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_sort_values.py',
   'DATA'),
  ('pandas/tests/indexes/datetimelike_/test_value_counts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimelike_/test_value_counts.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_asof.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_asof.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_delete.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_delete.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_factorize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_factorize.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_fillna.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_insert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_insert.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_isocalendar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_isocalendar.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_map.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_map.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_normalize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_normalize.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_repeat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_repeat.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_resolution.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_resolution.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_round.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_round.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_shift.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_shift.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_snap.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_snap.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_to_frame.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_frame.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_to_julian_date.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_julian_date.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_to_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_period.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_to_pydatetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_pydatetime.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_to_series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_to_series.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_tz_convert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_tz_convert.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_tz_localize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_tz_localize.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/methods/test_unique.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/methods/test_unique.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_date_range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_date_range.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_datetime.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_freq_attr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_freq_attr.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_iter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_iter.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_npfuncs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_npfuncs.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_ops.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_partial_slicing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_partial_slicing.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_pickle.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_reindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_reindex.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_scalar_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_scalar_compat.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/datetimes/test_timezones.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/datetimes/test_timezones.py',
   'DATA'),
  ('pandas/tests/indexes/interval/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_equals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_equals.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_interval.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_interval_range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_interval_range.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_interval_tree.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_interval_tree.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_pickle.py',
   'DATA'),
  ('pandas/tests/indexes/interval/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/interval/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/multi/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/multi/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/conftest.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_analytics.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_analytics.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_compat.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_conversion.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_conversion.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_copy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_copy.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_drop.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_drop.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_duplicates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_duplicates.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_equivalence.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_equivalence.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_get_level_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_get_level_values.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_get_set.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_get_set.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_integrity.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_integrity.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_isin.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_isin.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_lexsort.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_lexsort.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_missing.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_monotonic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_monotonic.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_names.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_names.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_partial_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_partial_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_pickle.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_reindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_reindex.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_reshape.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_reshape.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_sorting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_sorting.py',
   'DATA'),
  ('pandas/tests/indexes/multi/test_take.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/multi/test_take.py',
   'DATA'),
  ('pandas/tests/indexes/numeric/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/numeric/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/numeric/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/numeric/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/numeric/test_numeric.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_numeric.py',
   'DATA'),
  ('pandas/tests/indexes/numeric/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/numeric/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/object/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/object/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/object/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/object/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/object/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/object/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/period/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_asfreq.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_asfreq.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_factorize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_factorize.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_fillna.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_insert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_insert.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_is_full.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_is_full.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_repeat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_repeat.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_shift.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_shift.py',
   'DATA'),
  ('pandas/tests/indexes/period/methods/test_to_timestamp.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/methods/test_to_timestamp.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_freq_attr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_freq_attr.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_monotonic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_monotonic.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_partial_slicing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_partial_slicing.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_period.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_period_range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_period_range.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_pickle.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_resolution.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_resolution.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_scalar_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_scalar_compat.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_searchsorted.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_searchsorted.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/period/test_tools.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/period/test_tools.py',
   'DATA'),
  ('pandas/tests/indexes/ranges/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/ranges/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/ranges/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/ranges/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/ranges/test_range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_range.py',
   'DATA'),
  ('pandas/tests/indexes/ranges/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/ranges/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/string/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/string/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/string/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/string/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/string/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/string/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/test_any_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_any_index.py',
   'DATA'),
  ('pandas/tests/indexes/test_base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_base.py',
   'DATA'),
  ('pandas/tests/indexes/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_common.py',
   'DATA'),
  ('pandas/tests/indexes/test_datetimelike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_datetimelike.py',
   'DATA'),
  ('pandas/tests/indexes/test_engines.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_engines.py',
   'DATA'),
  ('pandas/tests/indexes/test_frozen.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_frozen.py',
   'DATA'),
  ('pandas/tests/indexes/test_index_new.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_index_new.py',
   'DATA'),
  ('pandas/tests/indexes/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/test_numpy_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_numpy_compat.py',
   'DATA'),
  ('pandas/tests/indexes/test_old_base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_old_base.py',
   'DATA'),
  ('pandas/tests/indexes/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/test_subclass.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/test_subclass.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/__init__.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_astype.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/test_factorize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_factorize.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_fillna.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/test_insert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_insert.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/test_repeat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_repeat.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/methods/test_shift.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/methods/test_shift.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_constructors.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_delete.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_delete.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_formats.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_freq_attr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_freq_attr.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_join.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_ops.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_pickle.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_scalar_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_scalar_compat.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_searchsorted.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_searchsorted.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_setops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_setops.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_timedelta.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_timedelta.py',
   'DATA'),
  ('pandas/tests/indexes/timedeltas/test_timedelta_range.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexes/timedeltas/test_timedelta_range.py',
   'DATA'),
  ('pandas/tests/indexing/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/__init__.py',
   'DATA'),
  ('pandas/tests/indexing/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/common.py',
   'DATA'),
  ('pandas/tests/indexing/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/conftest.py',
   'DATA'),
  ('pandas/tests/indexing/interval/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/interval/__init__.py',
   'DATA'),
  ('pandas/tests/indexing/interval/test_interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/interval/test_interval.py',
   'DATA'),
  ('pandas/tests/indexing/interval/test_interval_new.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/interval/test_interval_new.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/__init__.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_chaining_and_caching.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_chaining_and_caching.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_datetime.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_getitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_getitem.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_iloc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_iloc.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_indexing_slow.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_indexing_slow.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_loc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_loc.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_multiindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_multiindex.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_partial.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_partial.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_setitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_setitem.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_slice.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_slice.py',
   'DATA'),
  ('pandas/tests/indexing/multiindex/test_sorted.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/multiindex/test_sorted.py',
   'DATA'),
  ('pandas/tests/indexing/test_at.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_at.py',
   'DATA'),
  ('pandas/tests/indexing/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_categorical.py',
   'DATA'),
  ('pandas/tests/indexing/test_chaining_and_caching.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_chaining_and_caching.py',
   'DATA'),
  ('pandas/tests/indexing/test_check_indexer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_check_indexer.py',
   'DATA'),
  ('pandas/tests/indexing/test_coercion.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_coercion.py',
   'DATA'),
  ('pandas/tests/indexing/test_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_datetime.py',
   'DATA'),
  ('pandas/tests/indexing/test_floats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_floats.py',
   'DATA'),
  ('pandas/tests/indexing/test_iat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_iat.py',
   'DATA'),
  ('pandas/tests/indexing/test_iloc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_iloc.py',
   'DATA'),
  ('pandas/tests/indexing/test_indexers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_indexers.py',
   'DATA'),
  ('pandas/tests/indexing/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_indexing.py',
   'DATA'),
  ('pandas/tests/indexing/test_loc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_loc.py',
   'DATA'),
  ('pandas/tests/indexing/test_na_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_na_indexing.py',
   'DATA'),
  ('pandas/tests/indexing/test_partial.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_partial.py',
   'DATA'),
  ('pandas/tests/indexing/test_scalar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/indexing/test_scalar.py',
   'DATA'),
  ('pandas/tests/interchange/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/__init__.py',
   'DATA'),
  ('pandas/tests/interchange/test_impl.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/test_impl.py',
   'DATA'),
  ('pandas/tests/interchange/test_spec_conformance.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/test_spec_conformance.py',
   'DATA'),
  ('pandas/tests/interchange/test_utils.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/interchange/test_utils.py',
   'DATA'),
  ('pandas/tests/internals/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/__init__.py',
   'DATA'),
  ('pandas/tests/internals/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/test_api.py',
   'DATA'),
  ('pandas/tests/internals/test_internals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/test_internals.py',
   'DATA'),
  ('pandas/tests/internals/test_managers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/internals/test_managers.py',
   'DATA'),
  ('pandas/tests/io/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/__init__.py',
   'DATA'),
  ('pandas/tests/io/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/conftest.py',
   'DATA'),
  ('pandas/tests/io/excel/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/__init__.py',
   'DATA'),
  ('pandas/tests/io/excel/test_odf.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_odf.py',
   'DATA'),
  ('pandas/tests/io/excel/test_odswriter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_odswriter.py',
   'DATA'),
  ('pandas/tests/io/excel/test_openpyxl.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_openpyxl.py',
   'DATA'),
  ('pandas/tests/io/excel/test_readers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_readers.py',
   'DATA'),
  ('pandas/tests/io/excel/test_style.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_style.py',
   'DATA'),
  ('pandas/tests/io/excel/test_writers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_writers.py',
   'DATA'),
  ('pandas/tests/io/excel/test_xlrd.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_xlrd.py',
   'DATA'),
  ('pandas/tests/io/excel/test_xlsxwriter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/excel/test_xlsxwriter.py',
   'DATA'),
  ('pandas/tests/io/formats/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/__init__.py',
   'DATA'),
  ('pandas/tests/io/formats/style/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/__init__.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_bar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_bar.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_exceptions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_exceptions.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_format.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_format.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_highlight.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_highlight.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_html.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_html.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_matplotlib.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_matplotlib.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_non_unique.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_non_unique.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_style.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_style.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_to_latex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_to_latex.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_to_string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_to_string.py',
   'DATA'),
  ('pandas/tests/io/formats/style/test_tooltip.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/style/test_tooltip.py',
   'DATA'),
  ('pandas/tests/io/formats/test_console.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_console.py',
   'DATA'),
  ('pandas/tests/io/formats/test_css.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_css.py',
   'DATA'),
  ('pandas/tests/io/formats/test_eng_formatting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_eng_formatting.py',
   'DATA'),
  ('pandas/tests/io/formats/test_format.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_format.py',
   'DATA'),
  ('pandas/tests/io/formats/test_ipython_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_ipython_compat.py',
   'DATA'),
  ('pandas/tests/io/formats/test_printing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_printing.py',
   'DATA'),
  ('pandas/tests/io/formats/test_to_csv.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_csv.py',
   'DATA'),
  ('pandas/tests/io/formats/test_to_excel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_excel.py',
   'DATA'),
  ('pandas/tests/io/formats/test_to_html.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_html.py',
   'DATA'),
  ('pandas/tests/io/formats/test_to_latex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_latex.py',
   'DATA'),
  ('pandas/tests/io/formats/test_to_markdown.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_markdown.py',
   'DATA'),
  ('pandas/tests/io/formats/test_to_string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/formats/test_to_string.py',
   'DATA'),
  ('pandas/tests/io/generate_legacy_storage_files.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/generate_legacy_storage_files.py',
   'DATA'),
  ('pandas/tests/io/json/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/__init__.py',
   'DATA'),
  ('pandas/tests/io/json/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/conftest.py',
   'DATA'),
  ('pandas/tests/io/json/test_compression.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_compression.py',
   'DATA'),
  ('pandas/tests/io/json/test_deprecated_kwargs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_deprecated_kwargs.py',
   'DATA'),
  ('pandas/tests/io/json/test_json_table_schema.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_json_table_schema.py',
   'DATA'),
  ('pandas/tests/io/json/test_json_table_schema_ext_dtype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_json_table_schema_ext_dtype.py',
   'DATA'),
  ('pandas/tests/io/json/test_normalize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_normalize.py',
   'DATA'),
  ('pandas/tests/io/json/test_pandas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_pandas.py',
   'DATA'),
  ('pandas/tests/io/json/test_readlines.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_readlines.py',
   'DATA'),
  ('pandas/tests/io/json/test_ujson.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/json/test_ujson.py',
   'DATA'),
  ('pandas/tests/io/parser/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/__init__.py',
   'DATA'),
  ('pandas/tests/io/parser/common/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/__init__.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_chunksize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_chunksize.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_common_basic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_common_basic.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_data_list.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_data_list.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_decimal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_decimal.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_file_buffer_url.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_file_buffer_url.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_float.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_float.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_index.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_inf.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_inf.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_ints.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_ints.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_iterator.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_iterator.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_read_errors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_read_errors.py',
   'DATA'),
  ('pandas/tests/io/parser/common/test_verbose.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/common/test_verbose.py',
   'DATA'),
  ('pandas/tests/io/parser/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/conftest.py',
   'DATA'),
  ('pandas/tests/io/parser/dtypes/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/__init__.py',
   'DATA'),
  ('pandas/tests/io/parser/dtypes/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/test_categorical.py',
   'DATA'),
  ('pandas/tests/io/parser/dtypes/test_dtypes_basic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/test_dtypes_basic.py',
   'DATA'),
  ('pandas/tests/io/parser/dtypes/test_empty.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/dtypes/test_empty.py',
   'DATA'),
  ('pandas/tests/io/parser/test_c_parser_only.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_c_parser_only.py',
   'DATA'),
  ('pandas/tests/io/parser/test_comment.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_comment.py',
   'DATA'),
  ('pandas/tests/io/parser/test_compression.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_compression.py',
   'DATA'),
  ('pandas/tests/io/parser/test_concatenate_chunks.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_concatenate_chunks.py',
   'DATA'),
  ('pandas/tests/io/parser/test_converters.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_converters.py',
   'DATA'),
  ('pandas/tests/io/parser/test_dialect.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_dialect.py',
   'DATA'),
  ('pandas/tests/io/parser/test_encoding.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_encoding.py',
   'DATA'),
  ('pandas/tests/io/parser/test_header.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_header.py',
   'DATA'),
  ('pandas/tests/io/parser/test_index_col.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_index_col.py',
   'DATA'),
  ('pandas/tests/io/parser/test_mangle_dupes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_mangle_dupes.py',
   'DATA'),
  ('pandas/tests/io/parser/test_multi_thread.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_multi_thread.py',
   'DATA'),
  ('pandas/tests/io/parser/test_na_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_na_values.py',
   'DATA'),
  ('pandas/tests/io/parser/test_network.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_network.py',
   'DATA'),
  ('pandas/tests/io/parser/test_parse_dates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_parse_dates.py',
   'DATA'),
  ('pandas/tests/io/parser/test_python_parser_only.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_python_parser_only.py',
   'DATA'),
  ('pandas/tests/io/parser/test_quoting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_quoting.py',
   'DATA'),
  ('pandas/tests/io/parser/test_read_fwf.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_read_fwf.py',
   'DATA'),
  ('pandas/tests/io/parser/test_skiprows.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_skiprows.py',
   'DATA'),
  ('pandas/tests/io/parser/test_textreader.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_textreader.py',
   'DATA'),
  ('pandas/tests/io/parser/test_unsupported.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_unsupported.py',
   'DATA'),
  ('pandas/tests/io/parser/test_upcast.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/test_upcast.py',
   'DATA'),
  ('pandas/tests/io/parser/usecols/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/__init__.py',
   'DATA'),
  ('pandas/tests/io/parser/usecols/test_parse_dates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/test_parse_dates.py',
   'DATA'),
  ('pandas/tests/io/parser/usecols/test_strings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/test_strings.py',
   'DATA'),
  ('pandas/tests/io/parser/usecols/test_usecols_basic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/parser/usecols/test_usecols_basic.py',
   'DATA'),
  ('pandas/tests/io/pytables/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/__init__.py',
   'DATA'),
  ('pandas/tests/io/pytables/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/common.py',
   'DATA'),
  ('pandas/tests/io/pytables/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/conftest.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_append.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_append.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_categorical.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_compat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_compat.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_complex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_complex.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_errors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_errors.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_file_handling.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_file_handling.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_keys.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_keys.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_put.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_put.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_pytables_missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_pytables_missing.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_read.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_read.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_retain_attributes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_retain_attributes.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_round_trip.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_round_trip.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_select.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_select.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_store.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_store.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_subclass.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_subclass.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_time_series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_time_series.py',
   'DATA'),
  ('pandas/tests/io/pytables/test_timezones.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/pytables/test_timezones.py',
   'DATA'),
  ('pandas/tests/io/sas/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/__init__.py',
   'DATA'),
  ('pandas/tests/io/sas/test_byteswap.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_byteswap.py',
   'DATA'),
  ('pandas/tests/io/sas/test_sas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_sas.py',
   'DATA'),
  ('pandas/tests/io/sas/test_sas7bdat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_sas7bdat.py',
   'DATA'),
  ('pandas/tests/io/sas/test_xport.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/sas/test_xport.py',
   'DATA'),
  ('pandas/tests/io/test_clipboard.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_clipboard.py',
   'DATA'),
  ('pandas/tests/io/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_common.py',
   'DATA'),
  ('pandas/tests/io/test_compression.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_compression.py',
   'DATA'),
  ('pandas/tests/io/test_feather.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_feather.py',
   'DATA'),
  ('pandas/tests/io/test_fsspec.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_fsspec.py',
   'DATA'),
  ('pandas/tests/io/test_gbq.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_gbq.py',
   'DATA'),
  ('pandas/tests/io/test_gcs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_gcs.py',
   'DATA'),
  ('pandas/tests/io/test_html.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_html.py',
   'DATA'),
  ('pandas/tests/io/test_http_headers.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_http_headers.py',
   'DATA'),
  ('pandas/tests/io/test_orc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_orc.py',
   'DATA'),
  ('pandas/tests/io/test_parquet.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_parquet.py',
   'DATA'),
  ('pandas/tests/io/test_pickle.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_pickle.py',
   'DATA'),
  ('pandas/tests/io/test_s3.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_s3.py',
   'DATA'),
  ('pandas/tests/io/test_spss.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_spss.py',
   'DATA'),
  ('pandas/tests/io/test_sql.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_sql.py',
   'DATA'),
  ('pandas/tests/io/test_stata.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/test_stata.py',
   'DATA'),
  ('pandas/tests/io/xml/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/__init__.py',
   'DATA'),
  ('pandas/tests/io/xml/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/conftest.py',
   'DATA'),
  ('pandas/tests/io/xml/test_to_xml.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/test_to_xml.py',
   'DATA'),
  ('pandas/tests/io/xml/test_xml.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/test_xml.py',
   'DATA'),
  ('pandas/tests/io/xml/test_xml_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/io/xml/test_xml_dtypes.py',
   'DATA'),
  ('pandas/tests/libs/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/__init__.py',
   'DATA'),
  ('pandas/tests/libs/test_hashtable.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_hashtable.py',
   'DATA'),
  ('pandas/tests/libs/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_join.py',
   'DATA'),
  ('pandas/tests/libs/test_lib.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_lib.py',
   'DATA'),
  ('pandas/tests/libs/test_libalgos.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/libs/test_libalgos.py',
   'DATA'),
  ('pandas/tests/plotting/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/__init__.py',
   'DATA'),
  ('pandas/tests/plotting/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/common.py',
   'DATA'),
  ('pandas/tests/plotting/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/conftest.py',
   'DATA'),
  ('pandas/tests/plotting/frame/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/__init__.py',
   'DATA'),
  ('pandas/tests/plotting/frame/test_frame.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame.py',
   'DATA'),
  ('pandas/tests/plotting/frame/test_frame_color.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_color.py',
   'DATA'),
  ('pandas/tests/plotting/frame/test_frame_groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_groupby.py',
   'DATA'),
  ('pandas/tests/plotting/frame/test_frame_legend.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_legend.py',
   'DATA'),
  ('pandas/tests/plotting/frame/test_frame_subplots.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_frame_subplots.py',
   'DATA'),
  ('pandas/tests/plotting/frame/test_hist_box_by.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/frame/test_hist_box_by.py',
   'DATA'),
  ('pandas/tests/plotting/test_backend.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_backend.py',
   'DATA'),
  ('pandas/tests/plotting/test_boxplot_method.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_boxplot_method.py',
   'DATA'),
  ('pandas/tests/plotting/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_common.py',
   'DATA'),
  ('pandas/tests/plotting/test_converter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_converter.py',
   'DATA'),
  ('pandas/tests/plotting/test_datetimelike.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_datetimelike.py',
   'DATA'),
  ('pandas/tests/plotting/test_groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_groupby.py',
   'DATA'),
  ('pandas/tests/plotting/test_hist_method.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_hist_method.py',
   'DATA'),
  ('pandas/tests/plotting/test_misc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_misc.py',
   'DATA'),
  ('pandas/tests/plotting/test_series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_series.py',
   'DATA'),
  ('pandas/tests/plotting/test_style.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/plotting/test_style.py',
   'DATA'),
  ('pandas/tests/reductions/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reductions/__init__.py',
   'DATA'),
  ('pandas/tests/reductions/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reductions/test_reductions.py',
   'DATA'),
  ('pandas/tests/reductions/test_stat_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reductions/test_stat_reductions.py',
   'DATA'),
  ('pandas/tests/resample/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/__init__.py',
   'DATA'),
  ('pandas/tests/resample/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/conftest.py',
   'DATA'),
  ('pandas/tests/resample/test_base.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_base.py',
   'DATA'),
  ('pandas/tests/resample/test_datetime_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_datetime_index.py',
   'DATA'),
  ('pandas/tests/resample/test_period_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_period_index.py',
   'DATA'),
  ('pandas/tests/resample/test_resample_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_resample_api.py',
   'DATA'),
  ('pandas/tests/resample/test_resampler_grouper.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_resampler_grouper.py',
   'DATA'),
  ('pandas/tests/resample/test_time_grouper.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_time_grouper.py',
   'DATA'),
  ('pandas/tests/resample/test_timedelta.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/resample/test_timedelta.py',
   'DATA'),
  ('pandas/tests/reshape/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/__init__.py',
   'DATA'),
  ('pandas/tests/reshape/concat/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/__init__.py',
   'DATA'),
  ('pandas/tests/reshape/concat/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/conftest.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_append.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_append.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_append_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_append_common.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_categorical.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_categorical.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_concat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_concat.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_dataframe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_dataframe.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_datetimes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_datetimes.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_empty.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_empty.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_index.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_invalid.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_invalid.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_series.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_series.py',
   'DATA'),
  ('pandas/tests/reshape/concat/test_sort.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/concat/test_sort.py',
   'DATA'),
  ('pandas/tests/reshape/merge/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/__init__.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_join.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_join.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_merge.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_merge_asof.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_asof.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_merge_cross.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_cross.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_merge_index_as_string.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_index_as_string.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_merge_ordered.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_merge_ordered.py',
   'DATA'),
  ('pandas/tests/reshape/merge/test_multi.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/merge/test_multi.py',
   'DATA'),
  ('pandas/tests/reshape/test_crosstab.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_crosstab.py',
   'DATA'),
  ('pandas/tests/reshape/test_cut.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_cut.py',
   'DATA'),
  ('pandas/tests/reshape/test_from_dummies.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_from_dummies.py',
   'DATA'),
  ('pandas/tests/reshape/test_get_dummies.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_get_dummies.py',
   'DATA'),
  ('pandas/tests/reshape/test_melt.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_melt.py',
   'DATA'),
  ('pandas/tests/reshape/test_pivot.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_pivot.py',
   'DATA'),
  ('pandas/tests/reshape/test_pivot_multilevel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_pivot_multilevel.py',
   'DATA'),
  ('pandas/tests/reshape/test_qcut.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_qcut.py',
   'DATA'),
  ('pandas/tests/reshape/test_union_categoricals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_union_categoricals.py',
   'DATA'),
  ('pandas/tests/reshape/test_util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/reshape/test_util.py',
   'DATA'),
  ('pandas/tests/scalar/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/interval/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/interval/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/scalar/interval/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_constructors.py',
   'DATA'),
  ('pandas/tests/scalar/interval/test_contains.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_contains.py',
   'DATA'),
  ('pandas/tests/scalar/interval/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_formats.py',
   'DATA'),
  ('pandas/tests/scalar/interval/test_interval.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_interval.py',
   'DATA'),
  ('pandas/tests/scalar/interval/test_overlaps.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/interval/test_overlaps.py',
   'DATA'),
  ('pandas/tests/scalar/period/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/period/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/scalar/period/test_asfreq.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/test_asfreq.py',
   'DATA'),
  ('pandas/tests/scalar/period/test_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/period/test_period.py',
   'DATA'),
  ('pandas/tests/scalar/test_na_scalar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/test_na_scalar.py',
   'DATA'),
  ('pandas/tests/scalar/test_nat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/test_nat.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/methods/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/methods/test_as_unit.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/methods/test_as_unit.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/methods/test_round.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/methods/test_round.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_constructors.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_formats.py',
   'DATA'),
  ('pandas/tests/scalar/timedelta/test_timedelta.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timedelta/test_timedelta.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/__init__.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_as_unit.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_as_unit.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_normalize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_normalize.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_replace.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_round.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_round.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_timestamp_method.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_timestamp_method.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_to_julian_date.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_to_julian_date.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_to_pydatetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_to_pydatetime.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_tz_convert.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_tz_convert.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/methods/test_tz_localize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/methods/test_tz_localize.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/test_comparisons.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_comparisons.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_constructors.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_formats.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/test_timestamp.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_timestamp.py',
   'DATA'),
  ('pandas/tests/scalar/timestamp/test_timezones.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/scalar/timestamp/test_timezones.py',
   'DATA'),
  ('pandas/tests/series/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/__init__.py',
   'DATA'),
  ('pandas/tests/series/accessors/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/__init__.py',
   'DATA'),
  ('pandas/tests/series/accessors/test_cat_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_cat_accessor.py',
   'DATA'),
  ('pandas/tests/series/accessors/test_dt_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_dt_accessor.py',
   'DATA'),
  ('pandas/tests/series/accessors/test_list_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_list_accessor.py',
   'DATA'),
  ('pandas/tests/series/accessors/test_sparse_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_sparse_accessor.py',
   'DATA'),
  ('pandas/tests/series/accessors/test_str_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_str_accessor.py',
   'DATA'),
  ('pandas/tests/series/accessors/test_struct_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/accessors/test_struct_accessor.py',
   'DATA'),
  ('pandas/tests/series/indexing/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/__init__.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_datetime.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_delitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_delitem.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_get.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_get.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_getitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_getitem.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_indexing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_indexing.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_mask.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_mask.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_set_value.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_set_value.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_setitem.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_setitem.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_take.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_take.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_where.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_where.py',
   'DATA'),
  ('pandas/tests/series/indexing/test_xs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/indexing/test_xs.py',
   'DATA'),
  ('pandas/tests/series/methods/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/__init__.py',
   'DATA'),
  ('pandas/tests/series/methods/test_add_prefix_suffix.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_add_prefix_suffix.py',
   'DATA'),
  ('pandas/tests/series/methods/test_align.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_align.py',
   'DATA'),
  ('pandas/tests/series/methods/test_argsort.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_argsort.py',
   'DATA'),
  ('pandas/tests/series/methods/test_asof.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_asof.py',
   'DATA'),
  ('pandas/tests/series/methods/test_astype.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_astype.py',
   'DATA'),
  ('pandas/tests/series/methods/test_autocorr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_autocorr.py',
   'DATA'),
  ('pandas/tests/series/methods/test_between.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_between.py',
   'DATA'),
  ('pandas/tests/series/methods/test_case_when.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_case_when.py',
   'DATA'),
  ('pandas/tests/series/methods/test_clip.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_clip.py',
   'DATA'),
  ('pandas/tests/series/methods/test_combine.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_combine.py',
   'DATA'),
  ('pandas/tests/series/methods/test_combine_first.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_combine_first.py',
   'DATA'),
  ('pandas/tests/series/methods/test_compare.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_compare.py',
   'DATA'),
  ('pandas/tests/series/methods/test_convert_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_convert_dtypes.py',
   'DATA'),
  ('pandas/tests/series/methods/test_copy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_copy.py',
   'DATA'),
  ('pandas/tests/series/methods/test_count.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_count.py',
   'DATA'),
  ('pandas/tests/series/methods/test_cov_corr.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_cov_corr.py',
   'DATA'),
  ('pandas/tests/series/methods/test_describe.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_describe.py',
   'DATA'),
  ('pandas/tests/series/methods/test_diff.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_diff.py',
   'DATA'),
  ('pandas/tests/series/methods/test_drop.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_drop.py',
   'DATA'),
  ('pandas/tests/series/methods/test_drop_duplicates.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_drop_duplicates.py',
   'DATA'),
  ('pandas/tests/series/methods/test_dropna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_dropna.py',
   'DATA'),
  ('pandas/tests/series/methods/test_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_dtypes.py',
   'DATA'),
  ('pandas/tests/series/methods/test_duplicated.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_duplicated.py',
   'DATA'),
  ('pandas/tests/series/methods/test_equals.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_equals.py',
   'DATA'),
  ('pandas/tests/series/methods/test_explode.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_explode.py',
   'DATA'),
  ('pandas/tests/series/methods/test_fillna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_fillna.py',
   'DATA'),
  ('pandas/tests/series/methods/test_get_numeric_data.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_get_numeric_data.py',
   'DATA'),
  ('pandas/tests/series/methods/test_head_tail.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_head_tail.py',
   'DATA'),
  ('pandas/tests/series/methods/test_infer_objects.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_infer_objects.py',
   'DATA'),
  ('pandas/tests/series/methods/test_info.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_info.py',
   'DATA'),
  ('pandas/tests/series/methods/test_interpolate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_interpolate.py',
   'DATA'),
  ('pandas/tests/series/methods/test_is_monotonic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_is_monotonic.py',
   'DATA'),
  ('pandas/tests/series/methods/test_is_unique.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_is_unique.py',
   'DATA'),
  ('pandas/tests/series/methods/test_isin.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_isin.py',
   'DATA'),
  ('pandas/tests/series/methods/test_isna.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_isna.py',
   'DATA'),
  ('pandas/tests/series/methods/test_item.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_item.py',
   'DATA'),
  ('pandas/tests/series/methods/test_map.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_map.py',
   'DATA'),
  ('pandas/tests/series/methods/test_matmul.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_matmul.py',
   'DATA'),
  ('pandas/tests/series/methods/test_nlargest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_nlargest.py',
   'DATA'),
  ('pandas/tests/series/methods/test_nunique.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_nunique.py',
   'DATA'),
  ('pandas/tests/series/methods/test_pct_change.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_pct_change.py',
   'DATA'),
  ('pandas/tests/series/methods/test_pop.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_pop.py',
   'DATA'),
  ('pandas/tests/series/methods/test_quantile.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_quantile.py',
   'DATA'),
  ('pandas/tests/series/methods/test_rank.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_rank.py',
   'DATA'),
  ('pandas/tests/series/methods/test_reindex.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_reindex.py',
   'DATA'),
  ('pandas/tests/series/methods/test_reindex_like.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_reindex_like.py',
   'DATA'),
  ('pandas/tests/series/methods/test_rename.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_rename.py',
   'DATA'),
  ('pandas/tests/series/methods/test_rename_axis.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_rename_axis.py',
   'DATA'),
  ('pandas/tests/series/methods/test_repeat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_repeat.py',
   'DATA'),
  ('pandas/tests/series/methods/test_replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_replace.py',
   'DATA'),
  ('pandas/tests/series/methods/test_reset_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_reset_index.py',
   'DATA'),
  ('pandas/tests/series/methods/test_round.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_round.py',
   'DATA'),
  ('pandas/tests/series/methods/test_searchsorted.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_searchsorted.py',
   'DATA'),
  ('pandas/tests/series/methods/test_set_name.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_set_name.py',
   'DATA'),
  ('pandas/tests/series/methods/test_size.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_size.py',
   'DATA'),
  ('pandas/tests/series/methods/test_sort_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_sort_index.py',
   'DATA'),
  ('pandas/tests/series/methods/test_sort_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_sort_values.py',
   'DATA'),
  ('pandas/tests/series/methods/test_to_csv.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_csv.py',
   'DATA'),
  ('pandas/tests/series/methods/test_to_dict.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_dict.py',
   'DATA'),
  ('pandas/tests/series/methods/test_to_frame.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_frame.py',
   'DATA'),
  ('pandas/tests/series/methods/test_to_numpy.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_to_numpy.py',
   'DATA'),
  ('pandas/tests/series/methods/test_tolist.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_tolist.py',
   'DATA'),
  ('pandas/tests/series/methods/test_truncate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_truncate.py',
   'DATA'),
  ('pandas/tests/series/methods/test_tz_localize.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_tz_localize.py',
   'DATA'),
  ('pandas/tests/series/methods/test_unique.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_unique.py',
   'DATA'),
  ('pandas/tests/series/methods/test_unstack.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_unstack.py',
   'DATA'),
  ('pandas/tests/series/methods/test_update.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_update.py',
   'DATA'),
  ('pandas/tests/series/methods/test_value_counts.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_value_counts.py',
   'DATA'),
  ('pandas/tests/series/methods/test_values.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_values.py',
   'DATA'),
  ('pandas/tests/series/methods/test_view.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/methods/test_view.py',
   'DATA'),
  ('pandas/tests/series/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_api.py',
   'DATA'),
  ('pandas/tests/series/test_arithmetic.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_arithmetic.py',
   'DATA'),
  ('pandas/tests/series/test_constructors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_constructors.py',
   'DATA'),
  ('pandas/tests/series/test_cumulative.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_cumulative.py',
   'DATA'),
  ('pandas/tests/series/test_formats.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_formats.py',
   'DATA'),
  ('pandas/tests/series/test_iteration.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_iteration.py',
   'DATA'),
  ('pandas/tests/series/test_logical_ops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_logical_ops.py',
   'DATA'),
  ('pandas/tests/series/test_missing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_missing.py',
   'DATA'),
  ('pandas/tests/series/test_npfuncs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_npfuncs.py',
   'DATA'),
  ('pandas/tests/series/test_reductions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_reductions.py',
   'DATA'),
  ('pandas/tests/series/test_subclass.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_subclass.py',
   'DATA'),
  ('pandas/tests/series/test_ufunc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_ufunc.py',
   'DATA'),
  ('pandas/tests/series/test_unary.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_unary.py',
   'DATA'),
  ('pandas/tests/series/test_validate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/series/test_validate.py',
   'DATA'),
  ('pandas/tests/strings/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/__init__.py',
   'DATA'),
  ('pandas/tests/strings/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/conftest.py',
   'DATA'),
  ('pandas/tests/strings/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_api.py',
   'DATA'),
  ('pandas/tests/strings/test_case_justify.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_case_justify.py',
   'DATA'),
  ('pandas/tests/strings/test_cat.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_cat.py',
   'DATA'),
  ('pandas/tests/strings/test_extract.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_extract.py',
   'DATA'),
  ('pandas/tests/strings/test_find_replace.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_find_replace.py',
   'DATA'),
  ('pandas/tests/strings/test_get_dummies.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_get_dummies.py',
   'DATA'),
  ('pandas/tests/strings/test_split_partition.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_split_partition.py',
   'DATA'),
  ('pandas/tests/strings/test_string_array.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_string_array.py',
   'DATA'),
  ('pandas/tests/strings/test_strings.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/strings/test_strings.py',
   'DATA'),
  ('pandas/tests/test_aggregation.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_aggregation.py',
   'DATA'),
  ('pandas/tests/test_algos.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_algos.py',
   'DATA'),
  ('pandas/tests/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_common.py',
   'DATA'),
  ('pandas/tests/test_downstream.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_downstream.py',
   'DATA'),
  ('pandas/tests/test_errors.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_errors.py',
   'DATA'),
  ('pandas/tests/test_expressions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_expressions.py',
   'DATA'),
  ('pandas/tests/test_flags.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_flags.py',
   'DATA'),
  ('pandas/tests/test_multilevel.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_multilevel.py',
   'DATA'),
  ('pandas/tests/test_nanops.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_nanops.py',
   'DATA'),
  ('pandas/tests/test_optional_dependency.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_optional_dependency.py',
   'DATA'),
  ('pandas/tests/test_register_accessor.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_register_accessor.py',
   'DATA'),
  ('pandas/tests/test_sorting.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_sorting.py',
   'DATA'),
  ('pandas/tests/test_take.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/test_take.py',
   'DATA'),
  ('pandas/tests/tools/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/__init__.py',
   'DATA'),
  ('pandas/tests/tools/test_to_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_datetime.py',
   'DATA'),
  ('pandas/tests/tools/test_to_numeric.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_numeric.py',
   'DATA'),
  ('pandas/tests/tools/test_to_time.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_time.py',
   'DATA'),
  ('pandas/tests/tools/test_to_timedelta.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tools/test_to_timedelta.py',
   'DATA'),
  ('pandas/tests/tseries/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/__init__.py',
   'DATA'),
  ('pandas/tests/tseries/frequencies/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/__init__.py',
   'DATA'),
  ('pandas/tests/tseries/frequencies/test_freq_code.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/test_freq_code.py',
   'DATA'),
  ('pandas/tests/tseries/frequencies/test_frequencies.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/test_frequencies.py',
   'DATA'),
  ('pandas/tests/tseries/frequencies/test_inference.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/frequencies/test_inference.py',
   'DATA'),
  ('pandas/tests/tseries/holiday/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/__init__.py',
   'DATA'),
  ('pandas/tests/tseries/holiday/test_calendar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_calendar.py',
   'DATA'),
  ('pandas/tests/tseries/holiday/test_federal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_federal.py',
   'DATA'),
  ('pandas/tests/tseries/holiday/test_holiday.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_holiday.py',
   'DATA'),
  ('pandas/tests/tseries/holiday/test_observance.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/holiday/test_observance.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/__init__.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/common.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_business_day.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_day.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_business_hour.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_hour.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_business_month.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_month.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_business_quarter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_quarter.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_business_year.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_business_year.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_common.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_common.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_custom_business_day.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_custom_business_day.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_custom_business_hour.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_custom_business_hour.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_custom_business_month.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_custom_business_month.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_dst.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_dst.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_easter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_easter.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_fiscal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_fiscal.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_index.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_index.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_month.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_month.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_offsets.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_offsets.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_offsets_properties.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_offsets_properties.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_quarter.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_quarter.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_ticks.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_ticks.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_week.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_week.py',
   'DATA'),
  ('pandas/tests/tseries/offsets/test_year.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tseries/offsets/test_year.py',
   'DATA'),
  ('pandas/tests/tslibs/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/__init__.py',
   'DATA'),
  ('pandas/tests/tslibs/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_api.py',
   'DATA'),
  ('pandas/tests/tslibs/test_array_to_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_array_to_datetime.py',
   'DATA'),
  ('pandas/tests/tslibs/test_ccalendar.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_ccalendar.py',
   'DATA'),
  ('pandas/tests/tslibs/test_conversion.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_conversion.py',
   'DATA'),
  ('pandas/tests/tslibs/test_fields.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_fields.py',
   'DATA'),
  ('pandas/tests/tslibs/test_libfrequencies.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_libfrequencies.py',
   'DATA'),
  ('pandas/tests/tslibs/test_liboffsets.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_liboffsets.py',
   'DATA'),
  ('pandas/tests/tslibs/test_np_datetime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_np_datetime.py',
   'DATA'),
  ('pandas/tests/tslibs/test_npy_units.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_npy_units.py',
   'DATA'),
  ('pandas/tests/tslibs/test_parse_iso8601.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_parse_iso8601.py',
   'DATA'),
  ('pandas/tests/tslibs/test_parsing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_parsing.py',
   'DATA'),
  ('pandas/tests/tslibs/test_period.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_period.py',
   'DATA'),
  ('pandas/tests/tslibs/test_resolution.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_resolution.py',
   'DATA'),
  ('pandas/tests/tslibs/test_strptime.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_strptime.py',
   'DATA'),
  ('pandas/tests/tslibs/test_timedeltas.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_timedeltas.py',
   'DATA'),
  ('pandas/tests/tslibs/test_timezones.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_timezones.py',
   'DATA'),
  ('pandas/tests/tslibs/test_to_offset.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_to_offset.py',
   'DATA'),
  ('pandas/tests/tslibs/test_tzconversion.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/tslibs/test_tzconversion.py',
   'DATA'),
  ('pandas/tests/util/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/__init__.py',
   'DATA'),
  ('pandas/tests/util/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/conftest.py',
   'DATA'),
  ('pandas/tests/util/test_assert_almost_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_almost_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_attr_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_attr_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_categorical_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_categorical_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_extension_array_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_extension_array_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_frame_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_frame_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_index_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_index_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_interval_array_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_interval_array_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_numpy_array_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_numpy_array_equal.py',
   'DATA'),
  ('pandas/tests/util/test_assert_produces_warning.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_produces_warning.py',
   'DATA'),
  ('pandas/tests/util/test_assert_series_equal.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_assert_series_equal.py',
   'DATA'),
  ('pandas/tests/util/test_deprecate.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_deprecate.py',
   'DATA'),
  ('pandas/tests/util/test_deprecate_kwarg.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_deprecate_kwarg.py',
   'DATA'),
  ('pandas/tests/util/test_deprecate_nonkeyword_arguments.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_deprecate_nonkeyword_arguments.py',
   'DATA'),
  ('pandas/tests/util/test_doc.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_doc.py',
   'DATA'),
  ('pandas/tests/util/test_hashing.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_hashing.py',
   'DATA'),
  ('pandas/tests/util/test_numba.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_numba.py',
   'DATA'),
  ('pandas/tests/util/test_rewrite_warning.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_rewrite_warning.py',
   'DATA'),
  ('pandas/tests/util/test_shares_memory.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_shares_memory.py',
   'DATA'),
  ('pandas/tests/util/test_show_versions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_show_versions.py',
   'DATA'),
  ('pandas/tests/util/test_util.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_util.py',
   'DATA'),
  ('pandas/tests/util/test_validate_args.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_args.py',
   'DATA'),
  ('pandas/tests/util/test_validate_args_and_kwargs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_args_and_kwargs.py',
   'DATA'),
  ('pandas/tests/util/test_validate_inclusive.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_inclusive.py',
   'DATA'),
  ('pandas/tests/util/test_validate_kwargs.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/util/test_validate_kwargs.py',
   'DATA'),
  ('pandas/tests/window/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/__init__.py',
   'DATA'),
  ('pandas/tests/window/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/conftest.py',
   'DATA'),
  ('pandas/tests/window/moments/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/__init__.py',
   'DATA'),
  ('pandas/tests/window/moments/conftest.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/conftest.py',
   'DATA'),
  ('pandas/tests/window/moments/test_moments_consistency_ewm.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/test_moments_consistency_ewm.py',
   'DATA'),
  ('pandas/tests/window/moments/test_moments_consistency_expanding.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/test_moments_consistency_expanding.py',
   'DATA'),
  ('pandas/tests/window/moments/test_moments_consistency_rolling.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/moments/test_moments_consistency_rolling.py',
   'DATA'),
  ('pandas/tests/window/test_api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_api.py',
   'DATA'),
  ('pandas/tests/window/test_apply.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_apply.py',
   'DATA'),
  ('pandas/tests/window/test_base_indexer.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_base_indexer.py',
   'DATA'),
  ('pandas/tests/window/test_cython_aggregations.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_cython_aggregations.py',
   'DATA'),
  ('pandas/tests/window/test_dtypes.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_dtypes.py',
   'DATA'),
  ('pandas/tests/window/test_ewm.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_ewm.py',
   'DATA'),
  ('pandas/tests/window/test_expanding.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_expanding.py',
   'DATA'),
  ('pandas/tests/window/test_groupby.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_groupby.py',
   'DATA'),
  ('pandas/tests/window/test_numba.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_numba.py',
   'DATA'),
  ('pandas/tests/window/test_online.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_online.py',
   'DATA'),
  ('pandas/tests/window/test_pairwise.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_pairwise.py',
   'DATA'),
  ('pandas/tests/window/test_rolling.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling.py',
   'DATA'),
  ('pandas/tests/window/test_rolling_functions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling_functions.py',
   'DATA'),
  ('pandas/tests/window/test_rolling_quantile.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling_quantile.py',
   'DATA'),
  ('pandas/tests/window/test_rolling_skew_kurt.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_rolling_skew_kurt.py',
   'DATA'),
  ('pandas/tests/window/test_timeseries_window.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_timeseries_window.py',
   'DATA'),
  ('pandas/tests/window/test_win_type.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tests/window/test_win_type.py',
   'DATA'),
  ('pandas/tseries/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/__init__.py',
   'DATA'),
  ('pandas/tseries/api.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/api.py',
   'DATA'),
  ('pandas/tseries/frequencies.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/frequencies.py',
   'DATA'),
  ('pandas/tseries/holiday.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/holiday.py',
   'DATA'),
  ('pandas/tseries/offsets.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/tseries/offsets.py',
   'DATA'),
  ('pandas/util/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/__init__.py',
   'DATA'),
  ('pandas/util/_decorators.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_decorators.py',
   'DATA'),
  ('pandas/util/_doctools.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_doctools.py',
   'DATA'),
  ('pandas/util/_exceptions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_exceptions.py',
   'DATA'),
  ('pandas/util/_print_versions.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_print_versions.py',
   'DATA'),
  ('pandas/util/_test_decorators.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_test_decorators.py',
   'DATA'),
  ('pandas/util/_tester.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_tester.py',
   'DATA'),
  ('pandas/util/_validators.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/_validators.py',
   'DATA'),
  ('pandas/util/version/__init__.py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/util/version/__init__.py',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('Python3', 'Python3.framework/Versions/3.9/Python3', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/Data/WorkSpace/python/excel/build/Excel处理工具/base_library.zip',
   'DATA'),
  ('Python3.framework/Python3', 'Versions/Current/Python3', 'SYMLINK'),
  ('Python3.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python3.framework/Versions/3.9/Resources/Info.plist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Info.plist',
   'DATA'),
  ('Python3.framework/Versions/Current', '3.9', 'SYMLINK')],
 'Python3',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)

name: 构建Windows EXE文件

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:  # 允许手动触发

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: 升级pip
      run: |
        python -m pip install --upgrade pip
    
    - name: 安装依赖包
      run: |
        pip install -r requirements.txt
    
    - name: 显示Python和包信息
      run: |
        python --version
        pip list
    
    - name: 构建EXE文件
      run: |
        python -m PyInstaller --onefile --windowed --name="Excel处理工具" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=tkinter --hidden-import=tkinter.filedialog --hidden-import=tkinter.messagebox --collect-all=pandas --collect-all=openpyxl --noconfirm process_excel.py
    
    - name: 检查生成的文件
      run: |
        dir dist
        if (Test-Path "dist/Excel处理工具.exe") {
          $fileSize = (Get-Item "dist/Excel处理工具.exe").Length
          Write-Host "✅ EXE文件生成成功！"
          Write-Host "📁 文件大小: $([math]::Round($fileSize/1MB, 2)) MB"
        } else {
          Write-Host "❌ EXE文件生成失败！"
          exit 1
        }
    
    - name: 上传EXE文件
      uses: actions/upload-artifact@v4
      with:
        name: Excel处理工具-Windows-EXE
        path: dist/Excel处理工具.exe
        retention-days: 30
    
    - name: 创建发布版本（仅在推送到主分支时）
      if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v${{ github.run_number }}
        name: Excel处理工具 v${{ github.run_number }}
        body: |
          ## 📦 Excel处理工具 Windows版本
          
          ### 🎯 功能特点
          - ✅ 自动删除指定列（"预期评分"、"折扣"、"调整奖金"）
          - ✅ 公式转换为数值
          - ✅ 多工作表支持
          - ✅ 图形界面操作
          
          ### 💻 使用方法
          1. 下载 `Excel处理工具.exe`
          2. 双击运行
          3. 选择Excel文件
          4. 选择保存位置
          5. 等待处理完成
          
          ### 📋 系统要求
          - Windows 10/11
          - 无需安装Python或其他依赖
          
          ### 🔄 更新内容
          - 构建编号: ${{ github.run_number }}
          - 提交哈希: ${{ github.sha }}
          - 构建时间: ${{ github.event.head_commit.timestamp }}
        files: |
          dist/Excel处理工具.exe
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  test-functionality:
    runs-on: windows-latest
    needs: build-windows
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: 安装依赖包
      run: |
        pip install -r requirements.txt
    
    - name: 运行功能测试
      run: |
        python final_test.py
    
    - name: 下载构建的EXE
      uses: actions/download-artifact@v4
      with:
        name: Excel处理工具-Windows-EXE
        path: ./test-exe/
    
    - name: 验证EXE文件
      run: |
        if (Test-Path "test-exe/Excel处理工具.exe") {
          $fileInfo = Get-Item "test-exe/Excel处理工具.exe"
          Write-Host "✅ EXE文件验证成功！"
          Write-Host "📁 文件路径: $($fileInfo.FullName)"
          Write-Host "📊 文件大小: $([math]::Round($fileInfo.Length/1MB, 2)) MB"
          Write-Host "📅 创建时间: $($fileInfo.CreationTime)"
        } else {
          Write-Host "❌ EXE文件验证失败！"
          exit 1
        }
